# market_structure_analyzer.py

import pandas as pd
from typing import List, Dict, Any
from loguru import logger
from pivot_analyzer import PivotAnalyzer
from supertrend_analyzer import SuperTrendAnalyzer
from exceptions import InvalidDataError 

class MarketStructureAnalyzer:
    """
    ICT prensiplerine göre Market Structure Break (MSB) ve Market Structure Shift (MSS)
    tespit eder.
    YENİ VERSİYON: Trend belirlemede Supertrend'i birincil, pivot analizini ikincil yöntem olarak kullanır.
    """

    def __init__(self, mss_sensitivity: int = 5, msb_sensitivity: int = 5, 
                 pivot_analyzer: PivotAnalyzer = None, 
                 supertrend_analyzer: SuperTrendAnalyzer = None):  # <-- GÜNCELLENDİ
        """
        MarketStructureAnalyzer'ı başlatır.
        
        Args:
            mss_sensitivity: MSS tespiti için hassasiyet
            msb_sensitivity: MSB tespiti için hassasiyet  
            pivot_analyzer: PivotAnalyzer instance (DI için)
            supertrend_analyzer: SuperTrendAnalyzer instance (DI için) # <-- YENİ
        """
        self.mss_sensitivity = mss_sensitivity
        self.msb_sensitivity = msb_sensitivity 
        
        # Dependency Injection: PivotAnalyzer
        if pivot_analyzer is not None:
            self.mss_pivot_analyzer = pivot_analyzer
            logger.debug(f"MarketStructureAnalyzer: PivotAnalyzer DI ile alındı, MSS sensitivity: {mss_sensitivity}")
        else:
            self.mss_pivot_analyzer = PivotAnalyzer(length=mss_sensitivity)
            logger.debug(f"MarketStructureAnalyzer: Yeni PivotAnalyzer oluşturuldu, MSS sensitivity: {mss_sensitivity}")
        
        # Dependency Injection: SuperTrendAnalyzer # <-- YENİ BLOK
        if supertrend_analyzer is not None:
            self.supertrend_analyzer = supertrend_analyzer
            logger.debug("MarketStructureAnalyzer: SuperTrendAnalyzer DI ile enjekte edildi.")
        else:
            # Fallback - Eğer sağlanmazsa kendisi oluştursun
            from supertrend_analyzer import SuperTrendAnalyzer
            self.supertrend_analyzer = SuperTrendAnalyzer()
            logger.warning("MarketStructureAnalyzer: SuperTrendAnalyzer DI ile sağlanmadı, yeni bir tane oluşturuldu.")

        self.msb_pivot_analyzer = PivotAnalyzer(length=msb_sensitivity)
        logger.info(f"MarketStructureAnalyzer başlatıldı. MSS Hassasiyeti: {mss_sensitivity}, MSB Hassasiyeti: {msb_sensitivity}")


    def identify_all_swing_points(self, candles: pd.DataFrame, window: int = 5) -> List[Dict[str, Any]]:
        """
        Tüm swing noktalarını tespit eder. Her mum için sol ve sağındaki window mumdan daha yüksek/düşük olup olmadığını kontrol eder.
        """
        swings = []
        for i in range(window, len(candles) - window):
            current_high = candles.iloc[i]['high']
            current_low = candles.iloc[i]['low']
            is_high = all(current_high > candles.iloc[i - j]['high'] for j in range(1, window + 1)) and \
                      all(current_high > candles.iloc[i + j]['high'] for j in range(1, window + 1))
            is_low = all(current_low < candles.iloc[i - j]['low'] for j in range(1, window + 1)) and \
                     all(current_low < candles.iloc[i + j]['low'] for j in range(1, window + 1))
            if is_high:
                swings.append({'timestamp': candles.iloc[i]['timestamp'], 'price': current_high, 'type': 'high'})
            if is_low:
                swings.append({'timestamp': candles.iloc[i]['timestamp'], 'price': current_low, 'type': 'low'})
        return swings

    def analyze_structure_from_swings(self, swings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Swing noktalarından piyasa yapısını analiz eder: BOS, CHoCH vb.
        """
        results = {'bos': [], 'choch': [], 'hl': [], 'lh': []}
        if not swings:
            return results
        swings.sort(key=lambda x: x['timestamp'])
        prev_high = None
        prev_low = None
        for swing in swings:
            if swing['type'] == 'high':
                if prev_high:
                    if swing['price'] > prev_high['price']:
                        results['bos'].append({'type': 'bullish_bos', 'timestamp': swing['timestamp'], 'price': swing['price']})
                    else:
                        results['lh'].append({'type': 'lower_high', 'timestamp': swing['timestamp'], 'price': swing['price']})
                prev_high = swing
            elif swing['type'] == 'low':
                if prev_low:
                    if swing['price'] > prev_low['price']:
                        results['hl'].append({'type': 'higher_low', 'timestamp': swing['timestamp'], 'price': swing['price']})
                    else:
                        results['bos'].append({'type': 'bearish_bos', 'timestamp': swing['timestamp'], 'price': swing['price']})
                prev_low = swing
        # CHoCH mantığını ekle (örnek basit implementasyon)
        # Gerçek implementasyon için daha fazla logic eklenebilir
        return results

    def analyze(self, candles: pd.DataFrame) -> Dict[str, Any]: # <-- BU FONKSİYONU TAMAMEN DEĞİŞTİRİN
        """
        GÜNCELLENMİŞ HİBRİT MANTIK:
        1. Supertrend ile birincil trend yönünü belirler.
        2. Supertrend kararsız ise (fiyata çok yakın veya yatay), pivot analizini
           yardımcı olarak kullanarak daha derin bir yapısal analiz yapar.
        """
        results = {
            'breaks': [], 
            'idm_events': [],
            'major_pivots': [],
            'internal_pivots': [],
            'current_trend': 'sideways' # Varsayılan değer
        }
        # Minimum veri kontrolü - MSB sensitivity'yi de dikkate al
        mss_required = self.mss_sensitivity * 2 + 1  # MSS için gerekli minimum
        msb_required = self.msb_sensitivity * 2 + 1  # MSB için gerekli minimum
        min_required_candles = max(20, mss_required, msb_required)
        
        if candles is None or len(candles) < min_required_candles:
            raise InvalidDataError(f"Piyasa yapısı analizi için yetersiz mum verisi. Gerekli: {min_required_candles}, Mevcut: {len(candles) if candles is not None else 0}")

        # Adım 1: Supertrend analizini çalıştır
        try:
            st_analysis = self.supertrend_analyzer.analyze_candles("MARKET_STRUCTURE", "ANALYSIS", candles)
            st_trend = st_analysis.get("trend")
            st_distance_pct = st_analysis.get("distance_percent", 100)
            logger.debug(f"SuperTrend analizi: trend={st_trend}, distance={st_distance_pct}%")
        except Exception as e:
            logger.warning(f"SuperTrend analizi hatası: {e}, pivot analizine geçiliyor")
            st_trend = None
            st_distance_pct = 0
        
        current_trend = 'sideways'

        # Adım 2: Supertrend sonucuna göre karar ver
        if st_trend == 'up':
            current_trend = 'bullish'
            logger.info("Trend Tespiti (Birincil): Supertrend 'up' -> BULLISH")
        elif st_trend == 'down':
            current_trend = 'bearish'
            logger.info("Trend Tespiti (Birincil): Supertrend 'down' -> BEARISH")
        else:
            # Supertrend bir yön belirtmiyorsa (hata veya 'None' durumu), pivot analizini devreye sok
            if st_trend: # Bu durum normalde oluşmaz ama loglama için kalabilir
                logger.info(f"Trend Tespiti: Supertrend tanımsız bir durumda (trend={st_trend}), pivot analizi (İkincil) kullanılıyor...")
            else:
                logger.info("Trend Tespiti: Supertrend sonucu alınamadı, pivot analizi (İkincil) kullanılıyor...")
            major_pivots_raw = self.msb_pivot_analyzer.find_pivots(candles)
            current_trend = self._determine_trend_from_pivots(major_pivots_raw)
            results['major_pivots'] = major_pivots_raw # Pivotları sonuca ekle
        
        # Trend Gücü Hesaplaması
        trend_strength = self._calculate_trend_strength(current_trend, st_distance_pct, results.get('major_pivots', []))
        results['trend_strength'] = trend_strength

        results['current_trend'] = current_trend

        # Adım 3: Kalan analizleri standart şekilde çalıştır (pivotlar, kırılımlar vb.)
        if not results['major_pivots']: # Eğer pivotlar daha önce hesaplanmadıysa şimdi hesapla
            results['major_pivots'] = self.msb_pivot_analyzer.find_pivots(candles)

        results['internal_pivots'] = self.mss_pivot_analyzer.find_pivots(candles)
        
        idm_events = self._detect_inducement(candles, results['major_pivots'])
        results['idm_events'] = idm_events
        
        mss_events = self._find_breaks(candles, results['internal_pivots'], break_type='MSS', valid_pivots=results['major_pivots'])
        msb_events = self._find_breaks(candles, results['major_pivots'], break_type='MSB', valid_pivots=results['major_pivots'])
        
        all_events = mss_events + msb_events
        if all_events:
            all_events.sort(key=lambda x: x['timestamp'])
        results['breaks'] = all_events

        # En son BOS'u bul (fibonacci_analyzer için)
        latest_bos = None
        if all_events:
            # BOS tipindeki en son olayı bul
            bos_events = [event for event in all_events if 'BOS' in event.get('type', '').upper()]
            if bos_events:
                latest_bos = bos_events[-1]  # En son BOS
        results['latest_bos'] = latest_bos

        logger.info(f"HTF Trend (Final): {results['current_trend'].upper()} (Güç: {results.get('trend_strength', 'N/A'):.2f})")
        return results

    def _calculate_trend_strength(self, trend: str, st_distance_pct: float, pivots: List[Dict[str, Any]]) -> float:
        """
        Trendin gücünü 0.0 ile 1.0 arasında bir skor olarak hesaplar.

        Args:
            trend: 'bullish', 'bearish', veya 'sideways'.
            st_distance_pct: Supertrend'in fiyata olan yüzde uzaklığı.
            pivots: Ana pivot noktaları.

        Returns:
            Trend gücü skoru (0.0 - 1.0).
        """
        if trend == 'sideways':
            return 0.0

        # 1. Supertrend Uzaklık Skoru (Ağırlık: %60)
        # Uzaklık arttıkça trendin daha güçlü olduğunu varsayıyoruz.
        # 5% ve üzeri uzaklığı tam puan (1.0) olarak kabul edelim.
        st_score = min(st_distance_pct / 3.0, 1.0) 

        # 2. Pivot Yapısı Skoru (Ağırlık: %40)
        pivot_score = 0.0
        highs = sorted([p['price'] for p in pivots if p.get('type') == 'high'], reverse=True)
        lows = sorted([p['price'] for p in pivots if p.get('type') == 'low'], reverse=True)

        if trend == 'bullish' and len(highs) >= 2 and len(lows) >= 2:
            # Yükselen tepeler ve yükselen dipler
            if highs[0] > highs[1] and lows[0] > lows[1]:
                pivot_score = 1.0
            elif highs[0] > highs[1] or lows[0] > lows[1]:
                pivot_score = 0.5 # Sadece biri kurala uyuyor
        elif trend == 'bearish' and len(highs) >= 2 and len(lows) >= 2:
            # Alçalan tepeler ve alçalan dipler
            if highs[0] < highs[1] and lows[0] < lows[1]:
                pivot_score = 1.0
            elif highs[0] < highs[1] or lows[0] < lows[1]:
                pivot_score = 0.5 # Sadece biri kurala uyuyor

        # Ağırlıklı ortalama ile final skoru hesapla
        final_strength = (st_score * 0.6) + (pivot_score * 0.4)
        
        logger.debug(f"Trend Gücü Hesabı: Yön={trend}, ST_Skor={st_score:.2f}, Pivot_Skor={pivot_score:.2f} -> Final={final_strength:.2f}")

        return round(final_strength, 2)

    def _determine_trend_from_pivots(self, pivots: List[Dict[str, Any]]) -> str:
        """
        GÜNCELLENMİŞ VE DAHA ESNEK MANTIK:
        Son iki tepe ve son iki dip noktasının birbirine göre konumunu analiz ederek
        daha güvenilir bir trend yönü belirler.
        """
        if len(pivots) < 4:
            return 'sideways'  # Yeterli yapısal bilgi yok

        highs = [p for p in pivots if p.get('type') == 'high']
        lows = [p for p in pivots if p.get('type') == 'low']

    def check_multi_timeframe_alignment(self, htf_trend: str, ltf_structure: Dict[str, Any]) -> bool:
        """
        HTF trendiyle LTF piyasa yapısı (MSS/MSB) yönünün uyumunu kontrol eder.

        Args:
            htf_trend: Yüksek zaman dilimindeki trend ('bullish', 'bearish', 'sideways').
            ltf_structure: Düşük zaman dilimindeki yapı analizi sonucu.
                         Örnek: {'direction': 'bullish', 'type': 'MSS'}

        Returns:
            Uyum varsa True, yoksa False.
        """
        if not ltf_structure or 'direction' not in ltf_structure:
            logger.warning("LTF yapısı 'direction' anahtarı içermiyor.")
            return False
        
        ltf_direction = ltf_structure['direction']
        
        # Basit uyum kontrolü
        is_aligned = htf_trend == ltf_direction
        logger.info(f"Zaman Dilimi Uyum Kontrolü: HTF Trendi='{htf_trend.upper()}', LTF Yönü='{ltf_direction.upper()}' -> Uyumlu: {is_aligned}")
        return is_aligned

    def calculate_bos_entry(self, entry_price: float, confluence_zone: Dict[str, float]) -> bool:
        """
        Geri çekilmenin bir FVG/OB çakışma bölgesine ulaşıp ulaşmadığını kontrol eder.

        Args:
            entry_price: Geri çekilme sonrası potansiyel giriş fiyatı.
            confluence_zone: FVG/OB çakışma bölgesi. Örnek: {'high': 1.23, 'low': 1.22}

        Returns:
            Fiyat bölgenin içindeyse True, değilse False.
        """
        if not confluence_zone or 'high' not in confluence_zone or 'low' not in confluence_zone:
            logger.error("Geçersiz çakışma bölgesi (confluence_zone) formatı.")
            return False

        is_inside = confluence_zone['low'] <= entry_price <= confluence_zone['high']
        if is_inside:
            logger.info(f"Giriş Sinyali: Fiyat ({entry_price}) FVG/OB Çakışma Bölgesine ({confluence_zone}) ulaştı.")
        return is_inside

    def filter_weak_signals(self, signal: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """
        Zayıf MSS sinyallerini daha katı koşullara göre filtreler.

        Args:
            signal: İncelenecek işlem sinyali.
            context: Karar vermek için gereken ek bağlam (HTF trendi, likidite seviyeleri vb.).

        Returns:
            Sinyal güçlüyse True, zayıfsa False.
        """
        # Bu fonksiyonun tam implementasyonu, diğer modüllerden gelen verilere bağlıdır.
        # Örnek kontroller:
        is_strong = True
        if signal.get('type') == 'MSS':
            if not context.get('is_fvg_ob_ote_confluence'):
                is_strong = False
                logger.debug("Sinyal Filtrelendi: FVG-OB-OTE çakışması yok.")
            if not context.get('is_htf_aligned'):
                is_strong = False
                logger.debug("Sinyal Filtrelendi: HTF trendi ile uyumsuz.")
            if not context.get('is_near_liquidity'):
                is_strong = False
                logger.debug("Sinyal Filtrelendi: Likidite seviyesine yakın değil.")
            if not context.get('is_volume_imbalance_supported'):
                is_strong = False
                logger.debug("Sinyal Filtrelendi: Volume Imbalance ile desteklenmiyor.")
        
        return is_strong

    def invalidate_ob_by_htf_close(self, ob: Dict[str, float], htf_candle: Dict[str, float]) -> bool:
        """
        Bir Order Block'un (OB) HTF mum kapanışıyla geçersiz olup olmadığını kontrol eder.

        Args:
            ob: Order Block. Örnek: {'high': 1.23, 'low': 1.22, 'type': 'bullish'}
            htf_candle: Yüksek zaman dilimi mumu. Örnek: {'close': 1.21}

        Returns:
            OB geçersiz ise True, değilse False.
        """
        if not all(k in ob for k in ['high', 'low', 'type']) or 'close' not in htf_candle:
            logger.error("Geçersiz OB veya HTF mumu formatı.")
            return False

        htf_close = htf_candle['close']
        
        # Bullish OB için, kapanış OB'nin düşük seviyesinin altında olmamalıdır.
        if ob['type'] == 'bullish' and htf_close < ob['low']:
            logger.warning(f"Bullish OB Geçersiz Kılındı: HTF kapanışı ({htf_close}) OB'nin altındadır ({ob['low']}).")
            return True
        
        # Bearish OB için, kapanış OB'nin yüksek seviyesinin üstünde olmamalıdır.
        if ob['type'] == 'bearish' and htf_close > ob['high']:
            logger.warning(f"Bearish OB Geçersiz Kılındı: HTF kapanışı ({htf_close}) OB'nin üstündedir ({ob['high']}).")
            return True
            
        return False

        if len(highs) < 2 or len(lows) < 2:
            return 'sideways' # Karşılaştırma için yeterli tepe/dip yok

        # Son iki tepe ve son iki dibi al
        last_high = highs[-1]['price']
        prev_high = highs[-2]['price']
        last_low = lows[-1]['price']
        prev_low = lows[-2]['price']

        is_making_higher_highs = last_high > prev_high
        is_making_higher_lows = last_low > prev_low
        
        is_making_lower_highs = last_high < prev_high
        is_making_lower_lows = last_low < prev_low

        # Bullish Trend: Yükselen tepeler VE yükselen dipler
        if is_making_higher_highs and is_making_higher_lows:
            logger.info("Trend Tespiti (Pivot): Yükselen Tepeler ve Yükselen Dipler -> BULLISH")
            return 'bullish'

        # Bearish Trend: Alçalan tepeler VE alçalan dipler
        if is_making_lower_highs and is_making_lower_lows:
            logger.info("Trend Tespiti (Pivot): Alçalan Tepeler ve Alçalan Dipler -> BEARISH")
            return 'bearish'

        logger.info("Trend Tespiti (Pivot): Belirgin bir yapısal yön yok -> SIDEWAYS")
        return 'sideways' # Belirgin bir yapı yoksa

    def _find_breaks(self, candles: pd.DataFrame, pivots: List[Dict[str, Any]], break_type: str, valid_pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Verilen pivot listesine göre yapısal kırılımları (MSB veya MSS) bulur.
        Kırılım, bir pivot seviyesinin gövde kapanışıyla geçilmesidir.
        Ayrıca kırılımın bir IDM sonrası olup olmadığını kontrol eder.
        """
        breaks = []
        if not pivots:
            return breaks

        candles_indexed = candles.set_index('timestamp')
        processed_pivots = set()
        valid_pivot_timestamps = {p['timestamp'] for p in valid_pivots}

        for pivot in pivots:
            pivot_ts = pivot['timestamp']
            if pivot_ts in processed_pivots:
                continue

            subsequent_candles = candles_indexed.loc[candles_indexed.index > pivot_ts]

            for candle_ts, candle in subsequent_candles.iterrows():
                break_found = False
                is_idm_confirmed = pivot_ts in valid_pivot_timestamps

                if pivot['type'] == 'high' and candle['high'] > pivot['price']:
                    if candle['close'] > pivot['price']:
                        event = self._create_break_event(
                            break_type=break_type, direction='bullish',
                            candle_timestamp=candle_ts, candle_price=candle['close'],
                            broken_pivot=pivot)
                        event['idm_confirmed'] = is_idm_confirmed
                        breaks.append(event)
                        break_found = True

                elif pivot['type'] == 'low' and candle['low'] < pivot['price']:
                    if candle['close'] < pivot['price']:
                        event = self._create_break_event(
                            break_type=break_type, direction='bearish',
                            candle_timestamp=candle_ts, candle_price=candle['close'],
                            broken_pivot=pivot)
                        event['idm_confirmed'] = is_idm_confirmed
                        breaks.append(event)
                        break_found = True

                if break_found:
                    processed_pivots.add(pivot_ts)
                    break

        return breaks

    def _detect_inducement(self, candles: pd.DataFrame, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Gelişmiş Inducement (IDM) tespiti - ICT metodolojisine göre.
        
        IDM Kriterleri:
        🔍 False Breakout: %0.02-0.1 arası minimal kırılım
        🔍 Liquidity Grab: Çoklu test edilen seviyeler
        🔍 Round Number: .00, .50 seviyelerine yakınlık
        ✅ Confirmation: 5-15 bar sonrası ters yön hareketi
        
        Args:
            candles: Mum verileri
            pivots: Pivot noktaları
            
        Returns:
            List[Dict]: IDM pattern'leri
        """
        try:
            idm_events = []
            
            # IDM tespit parametreleri
            idm_min_penetration = 0.02  # %0.02 minimal kırılım
            idm_max_penetration = 0.1   # %0.1 maksimal kırılım
            idm_confirmation_bars = (5, 15)  # 5-15 bar sonrası ters yön hareketi
            
            for i, pivot in enumerate(pivots):
                # Pivot veri bütünlüğü kontrolü
                if not isinstance(pivot, dict):
                    logger.warning(f"IDM analizi: Geçersiz pivot verisi (index {i}): {type(pivot)}")
                    continue
                    
                pivot_price = pivot.get('price')
                pivot_type = pivot.get('type')
                pivot_index = pivot.get('index')
                
                # Kritik alanları kontrol et
                if not pivot_price or not pivot_type or pivot_index is None:
                    logger.warning(f"IDM analizi: Eksik pivot verisi (index {i}): price={pivot_price}, type={pivot_type}, index={pivot_index}")
                    continue
                
                # 1. False Breakout kontrolü
                false_breakout = self._check_false_breakout(candles, pivot_price, pivot_index, 
                                                          idm_min_penetration, idm_max_penetration)
                if not false_breakout:
                    continue
                
                # 2. Round Number yakınlığı
                is_round_number = self._check_round_number_proximity(pivot_price)
                
                # 3. Çoklu test kontrolü (Liquidity Grab)
                multiple_tests = self._check_multiple_tests(pivots, pivot_price)
                
                # 4. Confirmation kontrolü (5-15 bar sonrası ters hareket)
                confirmation = self._check_idm_confirmation(candles, pivot_index, pivot_type, idm_confirmation_bars)
                
                if confirmation:
                    # IDM kalitesini belirle
                    quality = 'IDM_CONFIRMED' if (is_round_number and multiple_tests >= 2) else 'STANDARD'
                    
                    idm_event = {
                        'type': 'IDM',
                        'direction': 'bullish' if pivot_type == 'low' else 'bearish',
                        'timestamp': pivot.get('timestamp', pd.Timestamp.now()),
                        'price': pivot_price,
                        'quality': quality,
                        'false_breakout': false_breakout,
                        'round_number': is_round_number,
                        'multiple_tests': multiple_tests,
                        'confirmation_bars': confirmation['bars'],
                        'reversal_strength': confirmation['strength']
                    }
                    
                    idm_events.append(idm_event)
                    logger.debug(f"IDM tespit edildi: {quality} @ {pivot_price} ({pivot_type})")
            
            logger.info(f"Market Structure: {len(idm_events)} IDM olayı tespit edildi")
            return idm_events
            
        except Exception as e:
            logger.error(f"IDM tespiti hatası: {e}", exc_info=True)
            return []
    
    def _check_false_breakout(self, candles: pd.DataFrame, price: float, index: int, 
                            min_pen: float, max_pen: float) -> bool:
        """False breakout kontrolü - minimal penetrasyon"""
        try:
            if index >= len(candles) - 5:
                return False
                
            # Sonraki 5 mumda penetrasyon kontrolü
            next_candles = candles.iloc[index:index+5]
            for _, candle in next_candles.iterrows():
                high_pen = abs(candle['high'] - price) / price
                low_pen = abs(candle['low'] - price) / price
                
                if min_pen <= max(high_pen, low_pen) <= max_pen:
                    return True
            return False
        except:
            return False
    
    def _check_round_number_proximity(self, price: float) -> bool:
        """Round number (.00, .50) yakınlığı kontrolü"""
        try:
            # Son iki dijiti al
            price_str = f"{price:.2f}"
            decimal_part = float(price_str.split('.')[-1])
            
            # .00, .50 seviyelerine yakınlık (%0.5 tolerans)
            return (abs(decimal_part - 0) <= 5 or 
                   abs(decimal_part - 50) <= 5 or
                   abs(decimal_part - 100) <= 5)
        except:
            return False
    
    def _check_multiple_tests(self, pivots: List[Dict], target_price: float) -> int:
        """Çoklu test kontrolü - aynı seviyeye kaç kez yaklaşılmış"""
        try:
            tolerance = target_price * 0.005  # %0.5 tolerans
            test_count = 0
            
            for pivot in pivots:
                pivot_price = pivot.get('price', 0)
                if abs(pivot_price - target_price) <= tolerance:
                    test_count += 1
                    
            return max(test_count - 1, 0)  # Kendisi hariç
        except:
            return 0
    
    def _check_idm_confirmation(self, candles: pd.DataFrame, index: int, pivot_type: str, 
                               confirmation_range: tuple) -> Dict[str, Any]:
        """IDM confirmation kontrolü - ters yön hareketi"""
        try:
            min_bars, max_bars = confirmation_range
            
            if index >= len(candles) - max_bars:
                return {}
                
            pivot_price = candles.iloc[index]['close']
            
            # Sonraki 5-15 mumda ters hareket arayalım
            for bars_ahead in range(min_bars, max_bars + 1):
                if index + bars_ahead >= len(candles):
                    break
                    
                future_candle = candles.iloc[index + bars_ahead]
                
                # Ters hareket kontrolü
                if pivot_type == 'low':  # Low'dan sonra yükseliş beklenir
                    if future_candle['close'] > pivot_price * 1.01:  # %1 yükseliş
                        strength = (future_candle['close'] - pivot_price) / pivot_price
                        return {'bars': bars_ahead, 'strength': strength}
                        
                elif pivot_type == 'high':  # High'dan sonra düşüş beklenir
                    if future_candle['close'] < pivot_price * 0.99:  # %1 düşüş
                        strength = (pivot_price - future_candle['close']) / pivot_price
                        return {'bars': bars_ahead, 'strength': strength}
            
            return {}
        except:
            return {}

    def _filter_valid_pivots(self, pivots: List[Dict[str, Any]], idm_events: List[Dict[str, Any]], 
                            current_trend: str = 'sideways') -> List[Dict[str, Any]]:
        """
        ICT kurallarına göre geçerli pivot noktalarını filtreleme
        
        YENİ: Sideways trend'de daha esnek kurallar uygulanır
        
        Özellikler:
        - ZigZag pattern validation (HH,HL,LH,LL sequences) 
        - IDM confirmation (Inducement sonrası onaylanmış pivotlar)
        - Pivot strength minimum thresholds
        - Clear market structure için False Break validation
        - Sideways trend'de gevşetilmiş kurallar
        """
        if not pivots:
            return []
            
        valid_pivots = []
        last_idm_ts = idm_events[-1]['timestamp'] if idm_events else pd.Timestamp.min
        
        # Sideways trend'de daha esnek kurallar
        is_sideways = current_trend == 'sideways'
        
        for i, pivot in enumerate(pivots):
            is_valid = True
            
            # 1. Temel geçerlilik kontrolü
            if not self._validate_pivot_basic(pivot):
                is_valid = False
                continue
            
            # 2. ICT Type Enrichment (HH/LL/LH/HL) - Moved from old _enrich_pivots_with_ict_types
            if i == 0:
                pivot['ict_type'] = 'N/A'  # İlk pivot karşılaştırılamaz
            else:
                pivot['ict_type'] = self._determine_ict_type(pivot, pivots[:i])
                
            # 3. ICT kuralı: IDM sonrası pivotlar daha güçlü (Sideways'de daha esnek)
            if idm_events and pivot['timestamp'] > last_idm_ts:
                pivot['post_idm'] = True  # IDM sonrası pivot olarak işaretle
                pivot['validation_strength'] = 'strong'
            else:
                pivot['post_idm'] = False
                # Sideways trend'de IDM'siz pivotları da orta güçte kabul et
                pivot['validation_strength'] = 'medium' if is_sideways else 'weak'
            
            # 4. Inducement validation: Pivot'un gerçek bir High/Low mu olduğunu kontrol et
            # Sideways trend'de bu kuralı gevşet
            if not is_sideways and not self._validate_pivot_inducement(pivot, pivots):
                # Normal trend'de Inducement olabilir ama yine de geçerli sayalım, sadece işaretle
                pivot['is_inducement'] = True
                pivot['validation_strength'] = 'weak'
            else:
                pivot['is_inducement'] = False
                # Sideways trend'de inducement kontrolünü atla veya gevşet
                if is_sideways:
                    pivot['is_inducement'] = False  # Sideways'de tüm pivotları kabul et
                
            # 5. False Break detection (Sideways'de daha toleranslı)
            if not is_sideways and self._detect_false_break(pivot, pivots):
                pivot['has_false_break'] = True
            else:
                pivot['has_false_break'] = False
                
            # Sideways trend'de tüm temel kriterleri geçen pivotları kabul et
            if is_sideways:
                is_valid = True  # Sideways'de katı filtreleme yapma
                
            if is_valid:
                valid_pivots.append(pivot)
        
        # Log mesajını güncelleyelim
        trend_note = " (Sideways - Esnek kurallar)" if is_sideways else " (Trending - Standart kurallar)"
        logger.info(f"[ICT Validation{trend_note}] {len(pivots)} pivottan {len(valid_pivots)} tanesi geçerli sayıldı.")
        logger.info(f"Post-IDM: {sum(1 for p in valid_pivots if p.get('post_idm', False))}, Inducement: {sum(1 for p in valid_pivots if p.get('is_inducement', False))}")
        
        return valid_pivots

    def _create_break_event(self, break_type: str, direction: str, candle_timestamp: pd.Timestamp, candle_price: float, broken_pivot: Dict[str, Any]) -> Dict[str, Any]:
        """Yardımcı fonksiyon: Kırılım olayı için bir sözlük oluşturur."""
        # Smart entry strategy için gerekli leg fiyatlarını hesapla
        # Leg start: kırılan pivot fiyatı, Leg end: kırılım fiyatı
        leg_start_price = broken_pivot['price']
        leg_end_price = candle_price
        
        return {
            'type': break_type,
            'direction': direction,
            'timestamp': candle_timestamp,
            'price': candle_price,
            'broken_pivot_price': broken_pivot['price'],
            'broken_pivot_timestamp': broken_pivot['timestamp'],
            'idm_confirmed': False, # Varsayılan değer
            # Smart entry strategy için gerekli alanlar
            'leg_start_price': leg_start_price,
            'leg_end_price': leg_end_price
        }
    
    def _validate_pivot_basic(self, pivot):
        """Pivot'un temel geçerlilik kontrolü"""
        return pivot and 'price' in pivot and 'candle_index' in pivot and 'type' in pivot
    
    def _validate_pivot_inducement(self, target_pivot, all_pivots):
        """
        Pivot'un gerçek bir High/Low mu yoksa Inducement mu olduğunu kontrol eder
        
        ICT Kuralı: Gerçek pivot, önceki structure'ı kırdıktan sonra karşı yönde 
        confirmation ile onaylanmalı
        """
        try:
            # Şu anki implementasyon basit - gelecekte candle data ile geliştirilecek
            pivot_type = target_pivot['type']
            pivot_price = target_pivot['price']
            pivot_time = target_pivot.get('timestamp', None)
            
            # Önceki aynı tip pivot'u bul
            previous_same_type = None
            for p in reversed(all_pivots):
                if p.get('timestamp', None) and pivot_time and p['timestamp'] < pivot_time and p['type'] == pivot_type:
                    previous_same_type = p
                    break
            
            if not previous_same_type:
                return True  # İlk pivot her zaman geçerli
                
            # Temel pattern kontrolü: Yeni pivot previous'ı anlamlı şekilde kırmış mı?
            price_diff_threshold = 0.001  # %0.1 minimum hareket
            
            if pivot_type == 'high':
                price_diff = abs(pivot_price - previous_same_type['price']) / previous_same_type['price']
                return price_diff > price_diff_threshold
            elif pivot_type == 'low':
                price_diff = abs(pivot_price - previous_same_type['price']) / previous_same_type['price']
                return price_diff > price_diff_threshold
                
            return True
            
        except Exception as e:
            logger.warning(f"Pivot inducement validation hatası: {e}")
            return True  # Hata durumunda pivot'u kabul et
    
    def _detect_false_break(self, pivot, all_pivots):
        """
        False Break detection: Önceki level'ı minimal kırdıktan sonra hızla geri dönen
        
        Şu anki implementasyon basit placeholder - gelecekte candle data ile geliştirilecek
        """
        try:
            # Basit false break detection: pivot fiyatı çok ekstrem değilse false break olabilir
            # Bu gerçek implementasyon için volume ve momentum analizi gerekir
            return False  # Şimdilik false break detection devre dışı
            
        except Exception as e:
            logger.warning(f"False break detection hatası: {e}")
            return False
    
    def _determine_ict_type(self, current_pivot: Dict[str, Any], previous_pivots: List[Dict[str, Any]]) -> str:
        """
        Mevcut pivot için ICT tipini belirler (HH/LL/LH/HL)
        
        Args:
            current_pivot: Analiz edilecek mevcut pivot
            previous_pivots: Önceki tüm pivotlar
            
        Returns:
            ICT tipi: 'HH', 'LH', 'LL', 'HL' veya 'N/A'
        """
        try:
            current_type = current_pivot.get('type', '')
            current_price = current_pivot.get('price', 0)
            
            if current_type == 'high':
                # Önceki high'ları bul
                prev_highs = [p for p in previous_pivots if p.get('type') == 'high']
                if prev_highs:
                    last_high = prev_highs[-1]
                    if current_price > last_high.get('price', 0):
                        return 'HH'  # Higher High
                    else:
                        return 'LH'  # Lower High
                else:
                    return 'HH'  # İlk high varsayılan olarak HH
                    
            elif current_type == 'low':
                # Önceki low'ları bul
                prev_lows = [p for p in previous_pivots if p.get('type') == 'low']
                if prev_lows:
                    last_low = prev_lows[-1]
                    if current_price < last_low.get('price', float('inf')):
                        return 'LL'  # Lower Low
                    else:
                        return 'HL'  # Higher Low
                else:
                    return 'LL'  # İlk low varsayılan olarak LL
            
            return 'N/A'
            
        except Exception as e:
            logger.warning(f"ICT type belirleme hatası: {e}")
            return 'N/A'

    def _filter_valid_pivots(self, pivots: List[Dict[str, Any]], idm_events: List[Dict[str, Any]], 
                            current_trend: str = 'sideways') -> List[Dict[str, Any]]:
        """
        ICT kurallarına göre geçerli pivot noktalarını filtreleme
        
        YENİ: Sideways trend'de daha esnek kurallar uygulanır
        
        Özellikler:
        - ZigZag pattern validation (HH,HL,LH,LL sequences) 
        - IDM confirmation (Inducement sonrası onaylanmış pivotlar)
        - Pivot strength minimum thresholds
        - Clear market structure için False Break validation
        - Sideways trend'de gevşetilmiş kurallar
        """
        if not pivots:
            return []
            
        valid_pivots = []
        last_idm_ts = idm_events[-1]['timestamp'] if idm_events else pd.Timestamp.min
        
        # Sideways trend'de daha esnek kurallar
        is_sideways = current_trend == 'sideways'
        
        for i, pivot in enumerate(pivots):
            is_valid = True
            
            # 1. Temel geçerlilik kontrolü
            if not self._validate_pivot_basic(pivot):
                is_valid = False
                continue
            
            # 2. ICT Type Enrichment (HH/LL/LH/HL) - Moved from old _enrich_pivots_with_ict_types
            if i == 0:
                pivot['ict_type'] = 'N/A'  # İlk pivot karşılaştırılamaz
            else:
                pivot['ict_type'] = self._determine_ict_type(pivot, pivots[:i])
                
            # 3. ICT kuralı: IDM sonrası pivotlar daha güçlü (Sideways'de daha esnek)
            if idm_events and pivot['timestamp'] > last_idm_ts:
                pivot['post_idm'] = True  # IDM sonrası pivot olarak işaretle
                pivot['validation_strength'] = 'strong'
            else:
                pivot['post_idm'] = False
                # Sideways trend'de IDM'siz pivotları da orta güçte kabul et
                pivot['validation_strength'] = 'medium' if is_sideways else 'weak'
            
            # 4. Inducement validation: Pivot'un gerçek bir High/Low mu olduğunu kontrol et
            # Sideways trend'de bu kuralı gevşet
            if not is_sideways and not self._validate_pivot_inducement(pivot, pivots):
                # Normal trend'de Inducement olabilir ama yine de geçerli sayalım, sadece işaretle
                pivot['is_inducement'] = True
                pivot['validation_strength'] = 'weak'
            else:
                pivot['is_inducement'] = False
                # Sideways trend'de inducement kontrolünü atla veya gevşet
                if is_sideways:
                    pivot['is_inducement'] = False  # Sideways'de tüm pivotları kabul et
                
            # 5. False Break detection (Sideways'de daha toleranslı)
            if not is_sideways and self._detect_false_break(pivot, pivots):
                pivot['has_false_break'] = True
            else:
                pivot['has_false_break'] = False
                
            # Sideways trend'de tüm temel kriterleri geçen pivotları kabul et
            if is_sideways:
                is_valid = True  # Sideways'de katı filtreleme yapma
                
            if is_valid:
                valid_pivots.append(pivot)
        
        # Log mesajını güncelleyelim
        trend_note = " (Sideways - Esnek kurallar)" if is_sideways else " (Trending - Standart kurallar)"
        logger.info(f"[ICT Validation{trend_note}] {len(pivots)} pivottan {len(valid_pivots)} tanesi geçerli sayıldı.")
        logger.info(f"Post-IDM: {sum(1 for p in valid_pivots if p.get('post_idm', False))}, Inducement: {sum(1 for p in valid_pivots if p.get('is_inducement', False))}")
        
        return valid_pivots

    def _create_break_event(self, break_type: str, direction: str, candle_timestamp: pd.Timestamp, candle_price: float, broken_pivot: Dict[str, Any]) -> Dict[str, Any]:
        """Yardımcı fonksiyon: Kırılım olayı için bir sözlük oluşturur."""
        # Smart entry strategy için gerekli leg fiyatlarını hesapla
        # Leg start: kırılan pivot fiyatı, Leg end: kırılım fiyatı
        leg_start_price = broken_pivot['price']
        leg_end_price = candle_price
        
        return {
            'type': break_type,
            'direction': direction,
            'timestamp': candle_timestamp,
            'price': candle_price,
            'broken_pivot_price': broken_pivot['price'],
            'broken_pivot_timestamp': broken_pivot['timestamp'],
            'idm_confirmed': False, # Varsayılan değer
            # Smart entry strategy için gerekli alanlar
            'leg_start_price': leg_start_price,
            'leg_end_price': leg_end_price
        }
    
    def _validate_pivot_basic(self, pivot):
        """Pivot'un temel geçerlilik kontrolü"""
        return pivot and 'price' in pivot and 'candle_index' in pivot and 'type' in pivot
    
    def _validate_pivot_inducement(self, target_pivot, all_pivots):
        """
        Pivot'un gerçek bir High/Low mu yoksa Inducement mu olduğunu kontrol eder
        
        ICT Kuralı: Gerçek pivot, önceki structure'ı kırdıktan sonra karşı yönde 
        confirmation ile onaylanmalı
        """
        try:
            # Şu anki implementasyon basit - gelecekte candle data ile geliştirilecek
            pivot_type = target_pivot['type']
            pivot_price = target_pivot['price']
            pivot_time = target_pivot.get('timestamp', None)
            
            # Önceki aynı tip pivot'u bul
            previous_same_type = None
            for p in reversed(all_pivots):
                if p.get('timestamp', None) and pivot_time and p['timestamp'] < pivot_time and p['type'] == pivot_type:
                    previous_same_type = p
                    break
            
            if not previous_same_type:
                return True  # İlk pivot her zaman geçerli
                
            # Temel pattern kontrolü: Yeni pivot previous'ı anlamlı şekilde kırmış mı?
            price_diff_threshold = 0.001  # %0.1 minimum hareket
            
            if pivot_type == 'high':
                price_diff = abs(pivot_price - previous_same_type['price']) / previous_same_type['price']
                return price_diff > price_diff_threshold
            elif pivot_type == 'low':
                price_diff = abs(pivot_price - previous_same_type['price']) / previous_same_type['price']
                return price_diff > price_diff_threshold
                
            return True
            
        except Exception as e:
            logger.warning(f"Pivot inducement validation hatası: {e}")
            return True  # Hata durumunda pivot'u kabul et
    
    def _detect_false_break(self, pivot, all_pivots):
        """
        False Break detection: Önceki level'ı minimal kırdıktan sonra hızla geri dönen
        
        Şu anki implementasyon basit placeholder - gelecekte candle data ile geliştirilecek
        """
        try:
            # Basit false break detection: pivot fiyatı çok ekstrem değilse false break olabilir
            # Bu gerçek implementasyon için volume ve momentum analizi gerekir
            return False  # Şimdilik false break detection devre dışı
            
        except Exception as e:
            logger.warning(f"False break detection hatası: {e}")
            return False
    
    def _determine_ict_type(self, current_pivot: Dict[str, Any], previous_pivots: List[Dict[str, Any]]) -> str:
        """
        Mevcut pivot için ICT tipini belirler (HH/LL/LH/HL)
        
        Args:
            current_pivot: Analiz edilecek mevcut pivot
            previous_pivots: Önceki tüm pivotlar
            
        Returns:
            ICT tipi: 'HH', 'LH', 'LL', 'HL' veya 'N/A'
        """
        try:
            current_type = current_pivot.get('type', '')
            current_price = current_pivot.get('price', 0)
            
            if current_type == 'high':
                # Önceki high'ları bul
                prev_highs = [p for p in previous_pivots if p.get('type') == 'high']
                if prev_highs:
                    last_high = prev_highs[-1]
                    if current_price > last_high.get('price', 0):
                        return 'HH'  # Higher High
                    else:
                        return 'LH'  # Lower High
                else:
                    return 'HH'  # İlk high varsayılan olarak HH
                    
            elif current_type == 'low':
                # Önceki low'ları bul
                prev_lows = [p for p in previous_pivots if p.get('type') == 'low']
                if prev_lows:
                    last_low = prev_lows[-1]
                    if current_price < last_low.get('price', float('inf')):
                        return 'LL'  # Lower Low
                    else:
                        return 'HL'  # Higher Low
                else:
                    return 'LL'  # İlk low varsayılan olarak LL
            
            return 'N/A'
            
        except Exception as e:
            logger.warning(f"ICT type belirleme hatası: {e}")
            return 'N/A'
