# htf_poi_ltf_mss_analyzer.py

from typing import Dict, Any, List, Optional
import pandas as pd
from loguru import logger

# Bağımlılıkları içe aktar
from market_structure_analyzer import MarketStructureAnalyzer
from pivot_analyzer import PivotAnalyzer # Gerekli olabilir
from fvg_analyzer import FvgAnalyzer
from liquidity_analyzer import LiquidityAnalyzer
from premium_discount_analyzer import PremiumDiscountAnalyzer

class HTFPoiLTFMssAnalyzer:
    """
    HTF POI (Point of Interest) Testi ve LTF Market Structure Shift stratejisi için
    analiz sınıfı. Klasik ICT giriş metodolojisini (12h/1h) uygular.
    
    Konsept:
    1. HTF (12h) zaman diliminde doldurulmamış FVG/OB tespit et
    2. Tespit edilen POI'nin Günlük Premium/Discount bölgesinde olduğunu doğrula
    3. Fiyatın bu HTF POI bölgesine girmesini bekle
    4. LTF (1h) zaman diliminde MSS/CHoCH ara
    5. LTF MSS sonrası pullback'te optimum giriş bul
    """
    
    def __init__(self, market_structure_analyzer: MarketStructureAnalyzer,
                 fvg_analyzer: FvgAnalyzer, 
                 liquidity_analyzer: LiquidityAnalyzer,
                 premium_discount_analyzer: PremiumDiscountAnalyzer):
        """
        HTF POI + LTF MSS (12h/1h) Analizörünü başlatır.
        
        Args:
            market_structure_analyzer: Market structure analiz için gerekli analizör
            fvg_analyzer: FVG analiz için gerekli analizör
            liquidity_analyzer: Likidite analiz için gerekli analizör
            premium_discount_analyzer: Premium/discount analiz için gerekli analizör
        """
        logger.info("HTF POI + LTF MSS (12h/1h) Analizörü başlatıldı")
        
        # Dependency Injection ile analizörleri al
        self.market_structure_analyzer = market_structure_analyzer
        self.fvg_analyzer = fvg_analyzer
        self.liquidity_analyzer = liquidity_analyzer
        self.premium_discount_analyzer = premium_discount_analyzer
        
        # HTF POI parametreleri (Sadece 12h)
        self.htf_timeframes = ['720']
        self.htf_poi_types = ['fvg', 'order_block']
        self.max_poi_mitigation_pct = 80  # POI'nin %80'den az doldurulmuş olması
        
        # LTF MSS parametreleri - ICT Standardı (Sadece 1h)
        self.ltf_timeframes = ['60']
        self.max_mss_age_candles = 10  # MSS'in son 10 mum içinde olması
        self.min_mss_strength = 3  # MSS gücü minimum 3/10
        
        # Confluence parametreleri
        self.min_confluence_score = 65  # Minimum %65 confluence skoru
        self.max_poi_distance_pct = 2.0  # Current price'dan maksimum %2 uzaklık
    
    def analyze(self, symbol: str, multi_tf_data: Dict[str, pd.DataFrame], 
               all_symbol_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        HTF POI + LTF MSS kombinasyonu için tam analiz yapar.
        """
        logger.info(f"[{symbol}] HTF POI + LTF MSS (12h/1h) analizi başlatılıyor...")
        
        htf_data = multi_tf_data.get('htf')
        ltf_data = multi_tf_data.get('ltf')
        daily_data = all_symbol_data.get('data', {}).get('D')

        if htf_data is None or htf_data.empty:
            logger.error(f"[{symbol}] HTF (12h) veri bulunamadı")
            return {'signals': [], 'error': 'htf_data_missing'}
        
        if ltf_data is None or ltf_data.empty:
            logger.error(f"[{symbol}] LTF (1h) veri bulunamadı")
            return {'signals': [], 'error': 'ltf_data_missing'}

        if daily_data is None or daily_data.empty:
            logger.error(f"[{symbol}] Günlük veri bulunamadı (Premium/Discount için gerekli)")
            return {'signals': [], 'error': 'daily_data_missing'}
            
        current_price = float(htf_data.iloc[-1]['close'])
        
        # Günlük Premium/Discount analizi
        # Gerekli verileri ana veri yapısından al
        daily_swing_points = all_symbol_data.get('swing_analysis_24h', {}).get('major_pivots')
        daily_stats = {'last_price': daily_data.iloc[-1]['close']} if daily_data is not None and not daily_data.empty else None

        daily_pd_analysis = self.premium_discount_analyzer.calculate_daily_premium_discount(
            symbol=symbol,
            daily_stats=daily_stats,
            swing_points=daily_swing_points
        )

        # FVG ve Likidite analizlerini çalıştır
        htf_structure = all_symbol_data.get('htf_12h_structure', {})
        all_pivots = htf_structure.get('major_pivots', []) + htf_structure.get('internal_pivots', [])
        fvg_analysis = self.fvg_analyzer.find_fvgs(htf_data, all_pivots)
        liquidity_analysis = self.liquidity_analyzer.analyze(htf_data, all_pivots)

        # 1. HTF POI'leri tespit et (Premium/Discount filtresi ile)
        htf_pois = self._identify_htf_pois(htf_data, all_symbol_data, current_price, fvg_analysis, liquidity_analysis, daily_pd_analysis)
        logger.info(f"[{symbol}] {len(htf_pois)} geçerli HTF POI tespit edildi")
        
        # 2. LTF MSS'leri tespit et
        ltf_mss_events = self._identify_ltf_mss(ltf_data, current_price)
        logger.info(f"[{symbol}] {len(ltf_mss_events)} LTF MSS tespit edildi")
        
        # 3. HTF POI + LTF MSS confluence'larını analiz et
        confluence_signals = self._analyze_poi_mss_confluence(
            htf_pois, ltf_mss_events, htf_data, current_price
        )
        
        # 4. Sinyalleri skorla ve filtrele
        scored_signals = self._score_and_filter_signals(confluence_signals, current_price)
        
        logger.success(f"[{symbol}] HTF POI + LTF MSS analizi tamamlandı: {len(scored_signals)} sinyal")
        
        return {
            'signals': scored_signals,
            'htf_pois_count': len(htf_pois),
            'ltf_mss_count': len(ltf_mss_events),
            'confluence_count': len(confluence_signals),
            'analysis_timestamp': pd.Timestamp.now(),
            'htf_timeframe': '720',
            'ltf_timeframe': '60'
        }
    
    def _identify_htf_pois(self, htf_data: pd.DataFrame, all_symbol_data: Dict[str, Any], 
                      current_price: float, fvg_analysis: List[Dict[str, Any]], 
                      liquidity_analysis: Dict[str, Any], daily_pd_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        HTF zaman diliminde POI'leri (FVG/Order Block) tespit eder.
        """
        htf_pois = []
        
        market_structure = all_symbol_data.get('market_structure', {})
        
        # FVG'leri analiz et
        if fvg_analysis:
            for fvg in fvg_analysis:
                poi = self._convert_fvg_to_poi(fvg, current_price, liquidity_analysis)
                if poi and self._is_valid_htf_poi(poi, current_price, market_structure, daily_pd_analysis):
                    htf_pois.append(poi)
        
        # Order Block'ları analiz et
        ob_analysis = all_symbol_data.get('order_block_analysis', {})
        if isinstance(ob_analysis, dict):
            for ob_type in ['bullish_obs', 'bearish_obs']:
                obs = ob_analysis.get(ob_type, [])
                for ob in obs:
                    poi = self._convert_ob_to_poi(ob, current_price)
                    if poi and self._is_valid_htf_poi(poi, current_price, market_structure, daily_pd_analysis):
                        htf_pois.append(poi)
        
        htf_pois.sort(key=lambda x: x.get('quality_score', 0), reverse=True)
        
        return htf_pois[:5]

    def _check_liquidity_confluence(self, fvg_top: float, fvg_bottom: float, 
                                    liquidity_analysis: Dict[str, Any]) -> bool:
        """Bir FVG'nin likidite havuzları ile kesişip kesişmediğini kontrol eder."""
        liquidity_pools = liquidity_analysis.get('liquidity_pools', {})
        
        for pool_type, pools in liquidity_pools.items():
            for pool in pools:
                pool_price = pool.get('price')
                if pool_price and fvg_bottom <= pool_price <= fvg_top:
                    logger.debug(f"FVG ({fvg_bottom}-{fvg_top}) likidite havuzu ile kesişiyor: {pool_price}")
                    return True
        return False
    
    def _convert_fvg_to_poi(self, fvg: Dict[str, Any], current_price: float, liquidity_analysis: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """FVG'yi HTF POI formatına çevirir."""
        try:
            fvg_top = fvg.get('top', 0)
            fvg_bottom = fvg.get('bottom', 0)
            fvg_type = fvg.get('type', 'unknown')
            
            if fvg_top <= 0 or fvg_bottom <= 0 or fvg_top <= fvg_bottom:
                return None
            
            mitigation_level = fvg.get('mitigation_percentage', 0)
            liquidity_confluence = self._check_liquidity_confluence(fvg_top, fvg_bottom, liquidity_analysis)
            quality_score = self._calculate_fvg_quality_score(fvg, current_price, liquidity_confluence)

            return {
                'poi_type': 'fvg',
                'direction': 'bullish' if fvg_type == 'bullish' else 'bearish',
                'top': fvg_top,
                'bottom': fvg_bottom,
                'mitigation_level': mitigation_level,
                'quality_score': quality_score,
                'invalidation_level': fvg_bottom if fvg_type == 'bullish' else fvg_top,
                'timeframe': fvg.get('timeframe', 'unknown'),
                'formation_time': fvg.get('start_time', ''),
                'liquidity_confluence': liquidity_confluence,
                'original_data': fvg
            }
        except Exception as e:
            logger.error(f"FVG to POI conversion error: {e}")
            return None
    
    def _convert_ob_to_poi(self, ob: Dict[str, Any], current_price: float) -> Optional[Dict[str, Any]]:
        """Order Block'u HTF POI formatına çevirir."""
        try:
            ob_top = ob.get('top', 0)
            ob_bottom = ob.get('bottom', 0)
            ob_type = ob.get('type', 'unknown')
            
            if ob_top <= 0 or ob_bottom <= 0 or ob_top <= ob_bottom:
                return None
            
            mitigation_level = ob.get('mitigation_percentage', 0)
            
            return {
                'poi_type': 'order_block',
                'direction': 'bullish' if ob_type == 'bullish' else 'bearish',
                'top': ob_top,
                'bottom': ob_bottom,
                'mitigation_level': mitigation_level,
                'quality_score': self._calculate_ob_quality_score(ob, current_price),
                'invalidation_level': ob_bottom if ob_type == 'bullish' else ob_top,
                'timeframe': ob.get('timeframe', 'unknown'),
                'formation_time': ob.get('start_time', ''),
                'original_data': ob
            }
        except Exception as e:
            logger.error(f"OB to POI conversion error: {e}")
            return None
    
    def _is_valid_htf_poi(self, poi: Dict[str, Any], current_price: float, market_structure: Dict[str, Any], daily_pd_analysis: Dict[str, Any]) -> bool:
        """
        GELİŞTİRİLDİ: Bir HTF POI'nin hala geçerli olup olmadığını kontrol eder.
        0. Premium/Discount Filtresi: POI, Günlük P/D aralığına uygun mu?
        1. Mitigasyon Durumu: POI daha önce test edilmiş mi?
        2. Mesafe: POI mevcut fiyata çok mu uzak?
        3. Kalite: POI'nin kendi içsel kalite skoru yeterli mi?
        4. Yapısal Geçersizleştirme: POI, karşıt bir yapı kırılımı ile geçersiz kılınmış mı?
        """
        symbol = poi.get('original_data', {}).get('symbol', 'N/A')
        
        # 0. YENİ: Günlük Premium/Discount Filtresi
        poi_direction = poi.get('direction')
        poi_price_point = poi.get('bottom') if poi_direction == 'bullish' else poi.get('top')
        
        is_in_pd_zone = self.premium_discount_analyzer.is_in_zone(
            price=poi_price_point,
            zone_type= 'discount' if poi_direction == 'bullish' else 'premium',
            pd_data=daily_pd_analysis
        )
        
        if not is_in_pd_zone:
            zone = 'Discount' if poi_direction == 'bullish' else 'Premium'
            logger.debug(f"[{symbol}] POI geçersiz: {poi_direction.capitalize()} POI, Günlük {zone} bölgesinde değil.")
            return False

        # 1. Mitigasyon Kontrolü
        if poi.get('mitigation_level', 100) > self.max_poi_mitigation_pct:
            logger.debug(f"[{symbol}] POI geçersiz: Mitigasyon seviyesi (%{poi.get('mitigation_level')}) > Eşik (%{self.max_poi_mitigation_pct})")
            return False
        
        # 2. Mesafe Kontrolü
        poi_center = (poi.get('top', 0) + poi.get('bottom', 0)) / 2
        if poi_center > 0:
            distance_pct = abs(poi_center - current_price) / current_price * 100
            if distance_pct > self.max_poi_distance_pct:
                logger.debug(f"[{symbol}] POI geçersiz: Mesafe (%{distance_pct:.2f}) > Eşik (%{self.max_poi_distance_pct})")
                return False
        
        # 3. Kalite Skoru Kontrolü
        if poi.get('quality_score', 0) < 40:
            logger.debug(f"[{symbol}] POI geçersiz: Kalite skoru (%{poi.get('quality_score')}) < Eşik (40)")
            return False

        # 4. Yapısal Geçersizleştirme Kontrolü
        breaks = market_structure.get('breaks', [])
        if not breaks:
            return True

        last_bos = max(breaks, key=lambda b: b.get('timestamp', 0)) if breaks else None

        if last_bos:
            bos_direction = last_bos.get('direction')
            bos_price = last_bos.get('price', 0)
            poi_top = poi.get('top', 0)
            poi_bottom = poi.get('bottom', 0)

            if poi_direction == 'bearish' and bos_direction == 'bullish' and bos_price > poi_top:
                logger.warning(f"[{symbol}] GEÇERSİZ POI: Bearish POI (Top: {poi_top}), son Bullish BOS (Price: {bos_price}) tarafından geçersiz kılındı.")
                return False
            
            if poi_direction == 'bullish' and bos_direction == 'bearish' and bos_price < poi_bottom:
                logger.warning(f"[{symbol}] GEÇERSİZ POI: Bullish POI (Bottom: {poi_bottom}), son Bearish BOS (Price: {bos_price}) tarafından geçersiz kılındı.")
                return False

        logger.debug(f"[{symbol}] POI tüm kontrollerden geçti, geçerli kabul ediliyor.")
        return True
    
    def _calculate_fvg_quality_score(self, fvg: Dict[str, Any], current_price: float, liquidity_confluence: bool) -> float:
        """FVG kalite skorunu hesaplar (0-100)."""
        score = 50.0  # Base score
        
        # Likidite Kesişimi Bonusu
        if liquidity_confluence:
            score += 25 # Puanlama 2.5'tan 25'e yükseltildi
        
        # Size factor (büyük FVG'ler daha kaliteli)
        fvg_size = fvg.get('top', 0) - fvg.get('bottom', 0)
        size_pct = (fvg_size / current_price) * 100
        if size_pct > 0.5:
            score += 20
        elif size_pct > 0.2:
            score += 10
        
        # Mitigation factor (az doldurulmuş FVG'ler daha kaliteli)
        mitigation = fvg.get('mitigation_percentage', 50)
        score += (100 - mitigation) * 0.2
        
        # Volume factor (eğer varsa)
        volume_factor = fvg.get('volume_factor', 1.0)
        if volume_factor > 1.5:
            score += 15
        elif volume_factor > 1.2:
            score += 10
        
        return min(100.0, max(0.0, score))
    
    def _calculate_ob_quality_score(self, ob: Dict[str, Any], current_price: float) -> float:
        """Order Block kalite skorunu hesaplar (0-100)."""
        score = 50.0  # Base score
        
        # Strength factor
        strength = ob.get('strength', 5)
        score += strength * 5  # 5 strength = +25 points
        
        # Size factor
        ob_size = ob.get('top', 0) - ob.get('bottom', 0)
        size_pct = (ob_size / current_price) * 100
        if size_pct > 0.3:
            score += 15
        elif size_pct > 0.1:
            score += 10
        
        # Mitigation factor
        mitigation = ob.get('mitigation_percentage', 50)
        score += (100 - mitigation) * 0.15
        
        return min(100.0, max(0.0, score))
    
    def _identify_ltf_mss(self, ltf_data: pd.DataFrame, current_price: float) -> List[Dict[str, Any]]:
        """ 
        GÜNCELLENMİŞ VE DOĞRU MANTIK:
        LTF zaman diliminde MarketStructureAnalyzer kullanarak gerçek MSS'leri tespit eder.
        """ 
        if ltf_data is None or ltf_data.empty:
            return []

        # MarketStructureAnalyzer'ı kullanarak LTF'deki tüm yapısal olayları analiz et
        ltf_structure = self.market_structure_analyzer.analyze(ltf_data)
        breaks = ltf_structure.get('breaks', [])

        mss_events = []
        for event in breaks:
            event_timestamp = event.get('timestamp')
            # Önce zaman damgasının varlığını ve ltf_data'nın indeksinde olup olmadığını kontrol et
            if event.get('type') == 'MSS' and pd.notna(event_timestamp) and event_timestamp in ltf_data.index:
                try:
                    # Mumun kaç sıra önce olduğunu güvenli bir şekilde hesapla
                    candles_ago = len(ltf_data) - ltf_data.index.get_loc(event_timestamp)
                    
                    # Sadece son 10 mum içinde olanları al
                    if candles_ago <= self.max_mss_age_candles:
                        mss_event = {
                            'mss_type': 'MSS',
                            'direction': event.get('direction'),
                            'candles_ago': candles_ago,
                            'strength': 7,  # Gerçek MSS olduğu için yüksek başlangıç gücü
                            'swing_high_price': event.get('broken_pivot_price') if event.get('direction') == 'bullish' else None,
                            'swing_low_price': event.get('broken_pivot_price') if event.get('direction') == 'bearish' else None,
                            'break_price': event.get('price'),
                            'swing_invalidation_level': event.get('broken_pivot_price'),
                            'timestamp': event_timestamp
                        }
                        mss_events.append(mss_event)
                except KeyError:
                    # get_loc yine de hata verirse (çok nadir bir durum), bunu yakala ve devam et
                    logger.warning(f"MSS event timestamp ({event_timestamp}) ltf_data içinde bulunamadı, atlanıyor.")
                    continue
        
        # En son olayları döndür
        mss_events.sort(key=lambda x: x['timestamp'], reverse=True)
        return mss_events[:3] # En son 3 MSS olayını al
    
    def _calculate_mss_strength(self, ltf_data: pd.DataFrame, swing_index: int, break_index: int) -> int:
        """MSS gücünü hesaplar (1-10 arası)."""
        try:
            # Volume factor
            avg_volume = ltf_data['volume'].rolling(10).mean().iloc[break_index]
            break_volume = ltf_data['volume'].iloc[break_index]
            volume_ratio = break_volume / avg_volume if avg_volume > 0 else 1.0
            
            # Distance factor
            swing_price = ltf_data['high'].iloc[swing_index] if break_index > swing_index else ltf_data['low'].iloc[swing_index]
            break_price = ltf_data['low'].iloc[break_index] if break_index > swing_index else ltf_data['high'].iloc[break_index]
            distance_pct = abs(break_price - swing_price) / swing_price * 100
            
            # Base strength
            strength = 5
            
            # Volume bonus
            if volume_ratio > 2.0:
                strength += 3
            elif volume_ratio > 1.5:
                strength += 2
            elif volume_ratio > 1.2:
                strength += 1
            
            # Distance bonus
            if distance_pct > 1.0:
                strength += 2
            elif distance_pct > 0.5:
                strength += 1
            
            return min(10, max(1, strength))
        except:
            return 5
    
    def _check_pullback_availability(self, ltf_data: pd.DataFrame, break_index: int, direction: str) -> bool:
        """MSS sonrası pullback'in mevcut olup olmadığını kontrol eder."""
        if break_index >= len(ltf_data) - 2:
            return False
        
        # Break sonrası 2-5 mum arasında pullback ara
        break_price = ltf_data['close'].iloc[break_index]
        
        for i in range(break_index + 1, min(break_index + 6, len(ltf_data))):
            current_price = ltf_data['close'].iloc[i]
            
            if direction == 'bullish':
                # Bullish MSS sonrası downward pullback
                if current_price < break_price * 0.998:  # %0.2 pullback
                    return True
            else:
                # Bearish MSS sonrası upward pullback
                if current_price > break_price * 1.002:  # %0.2 pullback
                    return True
        
        return False
    
    def _identify_ltf_entry_zones(self, ltf_data: pd.DataFrame, break_index: int, direction: str) -> List[Dict[str, Any]]:
        """MSS sonrası LTF entry zone'larını tespit eder."""
        entry_zones = []
        
        if break_index >= len(ltf_data) - 2:
            return entry_zones
        
        # MSS mumunun kendisi bir entry zone olabilir
        break_candle = ltf_data.iloc[break_index]
        
        if direction == 'bullish':
            # Bullish MSS için break mumunun low'u entry zone
            entry_zone = {
                'type': 'mss_candle_low',
                'optimal_price': float(break_candle['low']),
                'top': float(break_candle['high']),
                'bottom': float(break_candle['low']),
                'quality_score': 75,
                'confirmation_score': 80
            }
        else:
            # Bearish MSS için break mumunun high'ı entry zone
            entry_zone = {
                'type': 'mss_candle_high',
                'optimal_price': float(break_candle['high']),
                'top': float(break_candle['high']),
                'bottom': float(break_candle['low']),
                'quality_score': 75,
                'confirmation_score': 80
            }
        
        entry_zones.append(entry_zone)
        
        return entry_zones
    
    def _analyze_poi_mss_confluence(self, htf_pois: List[Dict[str, Any]], 
                                   ltf_mss_events: List[Dict[str, Any]],
                                   htf_data: pd.DataFrame,  # HTF mum verisi zaten alınıyor
                                   current_price: float) -> List[Dict[str, Any]]:
        """
        GELİŞTİRİLDİ: HTF POI'ler ile LTF MSS'ler arasındaki confluence'ları daha hassas analiz eder.
        MSS olayının yaşandığı andaki HTF mumunun POI bölgesini test edip etmediğini doğrular.
        """
        confluence_signals = []
        if not htf_pois or not ltf_mss_events or htf_data.empty:
            return []

        for poi in htf_pois:
            for mss in ltf_mss_events:
                # 1. Yön uyumluluğu kontrolü
                if poi.get('direction') != mss.get('direction'):
                    continue

                poi_bottom = poi.get('bottom', 0)
                poi_top = poi.get('top', 0)
                mss_timestamp = mss.get('timestamp')

                htf_candle_at_mss_time = None
                if pd.notna(mss_timestamp):
                    try:
                        # asof metodu ile mss_timestamp anındaki veya hemen önceki HTF mumunu bul
                        htf_candle_at_mss_time = htf_data.asof(mss_timestamp)
                    except Exception as e:
                        logger.warning(f"[{poi.get('symbol', 'N/A')}] HTF mumu 'asof' ile bulunamadı: {e}")

                if htf_candle_at_mss_time is None:
                    logger.debug(f"[{poi.get('symbol', 'N/A')}] LTF MSS ({mss_timestamp}) için karşılık gelen HTF mumu bulunamadı, bu konfluens atlanıyor.")
                    continue

                htf_high_at_mss = htf_candle_at_mss_time.get('high', 0)
                htf_low_at_mss = htf_candle_at_mss_time.get('low', 0)

                # Konumsal Kontrol: MSS anındaki HTF mumu, POI bölgesiyle etkileşime girdi mi?
                interaction_occurred = not (htf_high_at_mss < poi_bottom or htf_low_at_mss > poi_top)

                if interaction_occurred:
                    poi_type_str = poi.get('poi_type', 'Bilinmeyen POI')
                    logger.success(f"[{poi.get('symbol', 'N/A')}] ✅ Konfluens Doğrulandı: {mss.get('direction')} MSS, HTF {poi_type_str} bölgesini test ederken oluştu.")
                    confluence = {
                        'type': 'HTF_POI_LTF_MSS',
                        'pattern': 'HTF_POI_LTF_MSS',
                        'direction': poi['direction'].upper(),
                        'htf_poi_data': poi,
                        'ltf_mss_data': mss,
                        'confluence_score': self._calculate_confluence_score(poi, mss, current_price),
                        'current_price': current_price,
                        'entry_method': 'htf_poi_ltf_mss'
                    }
                    confluence_signals.append(confluence)
                else:
                    logger.debug(f"[{poi.get('symbol', 'N/A')}] Konfluens Reddedildi: MSS anındaki fiyat ({htf_low_at_mss}-{htf_high_at_mss}), POI ({poi_bottom}-{poi_top}) bölgesinden uzaktaydı.")

        return confluence_signals
    
    def _calculate_confluence_score(self, poi: Dict[str, Any], mss: Dict[str, Any], 
                                   current_price: float) -> float:
        """POI + MSS confluence kalite skorunu hesaplar."""
        score = 0.0
        
        # HTF POI kalitesi (40%)
        poi_quality = poi.get('quality_score', 50)
        score += (poi_quality / 100) * 40
        
        # LTF MSS kalitesi (35%)
        mss_strength = mss.get('strength', 5)
        mss_quality = (mss_strength / 10) * 100
        score += (mss_quality / 100) * 35
        
        # Timing confluence (15%)
        mss_recency = mss.get('candles_ago', 10)
        timing_score = max(0, 100 - (mss_recency * 10))
        score += (timing_score / 100) * 15
        
        # Direction confluence (10%)
        if poi['direction'] == mss['direction']:
            score += 10
        
        return min(100.0, max(0.0, score))
    
    def _score_and_filter_signals(self, confluence_signals: List[Dict[str, Any]], 
                                 current_price: float) -> List[Dict[str, Any]]:
        """Confluence sinyallerini skorla ve filtrele."""
        # Minimum confluence score filtresi
        valid_signals = [
            signal for signal in confluence_signals 
            if signal.get('confluence_score', 0) >= self.min_confluence_score
        ]
        
        # Confluence score'a göre sırala
        valid_signals.sort(key=lambda x: x.get('confluence_score', 0), reverse=True)
        
        # En iyi 2 sinyali döndür
        return valid_signals[:2]
