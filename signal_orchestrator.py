"""
Signal Orchestrator - Ana <PERSON>ü
==========================================

Bu modül ticaret sinyali belirlemede net sorumluluk ayrımı sağlar:

1. scoring_system._determine_trade_signal() → ANA SİNYAL TESPİTİ
   - ICT stratejilerine göre spesifik giriş fırsatları tespit eder
   - LIQSFP_REV, BRKR12_BOS1 gibi öncelikli kurulumları arar
   - Net BUY/SELL sinyali üretir

2. scoring_system._determine_trade_signal() → ANA SINYAL TESPİTİ
   - Genel piyasa yönü<PERSON> (BULLISH/BEARISH) belirler  
   - Ana sinyalin confluence faktörü olarak kullanılır
   - Piyasa rejimini (impulsive/corrective) tespit eder

3. SignalOrchestrator → ORKESTRASYON VE FİLTRELEME
   - Ana sinyal + piyasa yönü uyu<PERSON>nu kontrol eder
   - Risk-reward hesaplaması yapar
   - Son karar mekanizması
"""

from typing import Dict, Any, Optional, List
from loguru import logger
from datetime import datetime
from utils import format_price_standard, format_volume, shorten_indicator_names
import pandas as pd
# SignalOrchestrator ana sinyal koordinatörü



class SignalOrchestrator:
    """Ana sinyal koordinatörü - scoring_system ve smart_entry_strategy'yi orchestrate eder"""
    
    def __init__(self, scoring_system, smart_entry_strategy, session_manager=None, stats_tracker=None, confluence_aggregator=None):
        """
        Args:
            scoring_system: ScoringSystem instance - ana sinyal tespiti için
            smart_entry_strategy: SmartEntryStrategy instance - piyasa rejimi için
            session_manager: SessionManager instance - session analizi için (opsiyonel)
            stats_tracker: StatsTracker instance - istatistik ve kilit takibi için (opsiyonel)
            confluence_aggregator: ConfluenceAggregator instance - confluence analizi için (opsiyonel)
        """
        self.scoring_system = scoring_system
        self.smart_entry_strategy = smart_entry_strategy
        self.stats_tracker = stats_tracker
        self.confluence_aggregator = confluence_aggregator
        
        # Dependency Injection: SessionManager'ı constructor'da al veya lazy initialize et
        if session_manager is not None:
            self.session_manager = session_manager
        else:
            # Lazy initialization - ilk kullanımda oluştur
            self._session_manager = None
    
    @property
    def session_manager(self):
        """Lazy initialization ile SessionManager'ı döndür"""
        if self._session_manager is None:
            from session_manager import SessionManager
            self._session_manager = SessionManager()
            logger.debug("SessionManager lazy initialization ile oluşturuldu")
        return self._session_manager
    
    @session_manager.setter
    def session_manager(self, value):
        """SessionManager setter"""
        self._session_manager = value
        
    def determine_final_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Tüm potansiyel sinyalleri toplayıp, aralarından en iyisini seçerek final ticaret sinyalini oluşturur.
        "Tüm adayları topla, en iyisini seç" mantığını uygular.
        1. HTF Yönünü Belirle
        2. Tüm potansiyel sinyalleri topla
        3. Trend ve skora göre en iyi sinyali seç
        4. Giriş/SL/TP seviyelerini hesapla
        5. Final sinyalini paketle
        """
        logger.info(f"[{symbol}] === SİNYAL ORKESTRASYONU BAŞLIYOR (Yeni Mantık) ===")
        
        # Adım 0: Veri Doğrulama (Pre-flight Check)
        if not self._validate_input_data(symbol, all_symbol_data):
            return None # Hata zaten loglandı, sessizce çık

        # Gerekli verileri al
        structure_analysis = all_symbol_data.get('structure_analysis', {})
        candles = all_symbol_data.get('candles')
        if candles is None or candles.empty:
            logger.warning(f"[{symbol}] Mum verisi yok, orkestrasyon durduruldu.")
            return None
        current_price = candles.iloc[-1]['close']

        # Session Manager'dan mevcut durumu al
        time_analysis = self.session_manager.is_high_impact_time()
        logger.info(f"[{symbol}] 🕐 Zaman Analizi: Impact={time_analysis['impact_level']} ({time_analysis['impact_score']}/100)")

        # Adım 1: HTF Yönünü Belirle
        logger.info(f"[{symbol}] Adım 1: Piyasa Yönü Belirleniyor...")
        htf_direction = structure_analysis.get('current_trend', 'sideways').upper()
        
        # market_structure_analyzer'dan dinamik trend gücünü al
        htf_trend_strength = structure_analysis.get('trend_strength', 0.0)
        
        # Yön etiketini düzelt
        if htf_direction == 'SIDEWAYS':
            htf_direction = 'NEUTRAL'
        
        logger.info(f"[{symbol}] HTF Yönü: {htf_direction}, Güç: {htf_trend_strength:.2f}")

        if htf_direction == 'NEUTRAL' or htf_trend_strength < 0.40:
            logger.warning(f"[{symbol}] HTF yönü belirsiz veya güven düşük. İşlem aranmıyor.")
            return None

        # YENİ ADIM: Aktif bir beklenti (kilit) var mı kontrol et
        active_lock = self.stats_tracker.get_active_lock(symbol) if self.stats_tracker else None

        # Adım 2: Tüm Potansiyel ICT Sinyallerini Topla
        potential_signals = self.scoring_system._determine_trade_signal(symbol, all_symbol_data)
        
        if not potential_signals:
            # ...
            return None

        # YENİ ADIM: Sinyalleri aktif kilide göre filtrele
        if active_lock:
            original_count = len(potential_signals)
            expected_dir = active_lock['expected_direction']
            potential_signals = [
                s for s in potential_signals 
                if s.get('direction', '').lower() == expected_dir
            ]
            if len(potential_signals) < original_count:
                logger.warning(f"[{symbol}] {original_count - len(potential_signals)} sinyal, aktif BOS kilidi ({expected_dir}) nedeniyle filtrelendi.")

        # Adım 3: En İyi Sinyali Seç
        main_ict_signal = self._select_best_signal_from_list(potential_signals, htf_direction, symbol)
        
        if not main_ict_signal:
            logger.warning(f"[{symbol}] ⚠️ Potansiyel sinyaller bulundu ancak hiçbiri seçim kriterlerini karşılamadı.")
            return None
        
        signal_priority = main_ict_signal.get('selection_reason', 'SELECTED')
        logger.success(f"[{symbol}] ✅ {signal_priority} Sinyali Seçildi: {main_ict_signal.get('type')} ({main_ict_signal.get('direction')})")

        # Sinyali zamanlama ve diğer bilgilerle zenginleştir
        main_ict_signal['signal_priority'] = signal_priority
        main_ict_signal['time_analysis'] = time_analysis
        main_ict_signal['session_context'] = {
            'active_killzone': time_analysis['current_killzone']['display_name'] if time_analysis['current_killzone'] else None,
            'active_overlap': time_analysis['current_overlap']['display_name'] if time_analysis['current_overlap'] else None,
            'impact_level': time_analysis['impact_level'],
            'impact_score': time_analysis['impact_score']
        }
        main_ict_signal['active_killzone'] = {'name': time_analysis['current_killzone']['display_name'] if time_analysis['current_killzone'] else 'Aktif Değil'}
        main_ict_signal['active_overlap'] = time_analysis['current_overlap']['display_name'] if time_analysis['current_overlap'] else 'Aktif Değil'
        main_ict_signal['impact'] = {'level': time_analysis['impact_level'], 'score': time_analysis['impact_score']}
        main_ict_signal['priority'] = signal_priority
        
        poi_direction = main_ict_signal.get('direction', '').lower()

        # Adım 4: Giriş, SL ve TP Seviyelerini Hesapla
        logger.info(f"[{symbol}] Adım 4: Giriş/SL/TP Seviyeleri Hesaplanıyor...")
        
        # BOS/MSS sinyalleri için özel Fibonacci verilerini kullan (Tek Doğruluk Kaynağı prensibi)
        fibonacci_data_to_use = all_symbol_data.get('fibonacci_analysis')  # Varsayılan: Premium/Discount analizi
        signal_type = main_ict_signal.get('type', '')

        if 'BOS' in signal_type or 'MSS' in signal_type:
            bos_fib_analysis = all_symbol_data.get('bos_fibonacci_analysis')
            if bos_fib_analysis and not bos_fib_analysis.get('error'):
                fibonacci_data_to_use = bos_fib_analysis
                logger.info(f"[{symbol}] BOS/MSS sinyali için özel Fibonacci seviyeleri kullanılıyor")
            else:
                logger.warning(f"[{symbol}] BOS/MSS sinyali için Fibonacci seviyeleri bulunamadı, varsayılan kullanılıyor")
        
        entry_levels = self.smart_entry_strategy.calculate_entry_levels(
            symbol=symbol,
            stats={'last_price': current_price},
            trade_direction=poi_direction,
            fibonacci_data=fibonacci_data_to_use,
            order_blocks=all_symbol_data.get('order_block_analysis'),
            swing_points=structure_analysis.get('major_pivots', []),
            candles=candles,
            fvg_data=all_symbol_data.get('fvg_analysis'),
            pattern_details=main_ict_signal,
            liquidity_data=all_symbol_data.get('liquidity_analysis'),
            all_symbol_data=all_symbol_data
        )
        
        if not entry_levels or not entry_levels.get("primary_entry"):
            logger.error(f"[{symbol}] Geçerli bir giriş seviyesi bulunamadı. Strateji: {entry_levels.get('strategy_used') if entry_levels else 'None'}")
            return None
            
        logger.success(f"[{symbol}] ✅ Giriş Seviyeleri Hesaplandı. Strateji: {entry_levels.get('strategy_used')}")

        # Adım 5: Final Sinyalini Oluştur ve Döndür
        logger.info(f"[{symbol}] Adım 5: Final Sinyal Paketleniyor...")
        final_signal = main_ict_signal.copy()
        final_signal.update(entry_levels)
        final_signal['symbol'] = symbol
        final_signal['htf_direction'] = htf_direction
        final_signal['htf_trend'] = htf_direction
        final_signal['htf_trend_strength'] = htf_trend_strength
        final_signal['confidence'] = final_signal.get('confluence_score', 0.0) / 100.0

        self.log_final_signal_summary(symbol, final_signal)
        
        return final_signal

    def _select_best_signal_from_list(self, potential_signals: List[Dict[str, Any]], htf_direction: str, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Potansiyel sinyaller listesinden en uygun olanını seçer.
        
        YENİ MANTIK - Sinyal Önceliklendirme ve Trend Uyumu:
        Kural 1: Trend Önceliği - HTF yönü ile uyumlu sinyalleri her zaman önceliklendir
        Kural 2: Skor Önceliği - Eğer listedeki tüm sinyaller HTF yönü ile uyumsuzsa (reversal senaryoları), 
                 o zaman en yüksek confluence_score'a sahip olanı seç
        Kural 3: Sinyal Yok - Eğer listede hiç sinyal yoksa veya hiçbir sinyal kriterleri karşılamazsa None döndür
        
        Args:
            potential_signals: Potansiyel sinyaller listesi
            htf_direction: HTF trend yönü (BULLISH/BEARISH)
            symbol: Ticaret sembolü
            
        Returns:
            En uygun sinyal veya None
        """
        if not potential_signals:
            return None
            
        logger.info(f"[{symbol}] Sinyal seçim süreci başlıyor: {len(potential_signals)} aday sinyal")
        
        # Tüm sinyalleri HTF yönü ile uyumuna göre kategorize et
        htf_compatible_signals = []
        htf_incompatible_signals = []
        
        for signal in potential_signals:
            signal_direction = signal.get('direction', '').lower()
            signal_type = signal.get('type', '')
            confluence_score = signal.get('confluence_score', 0)
            
            # Minimum kalite kontrolü
            if confluence_score < 30:  # Çok düşük kaliteli sinyalleri filtrele
                logger.debug(f"[{symbol}] Düşük kalite sinyal filtrelendi: {signal_type} (Skor: {confluence_score})")
                continue
            
            # HTF yönü ile uyum kontrolü
            is_htf_compatible = False
            if htf_direction == 'BULLISH' and signal_direction in ['bull', 'bullish', 'buy', 'long']:
                is_htf_compatible = True
            elif htf_direction == 'BEARISH' and signal_direction in ['bear', 'bearish', 'sell', 'short']:
                is_htf_compatible = True
            
            if is_htf_compatible:
                htf_compatible_signals.append(signal)
                logger.debug(f"[{symbol}] HTF uyumlu sinyal: {signal_type} ({signal_direction}) - Skor: {confluence_score}")
            else:
                htf_incompatible_signals.append(signal)
                logger.debug(f"[{symbol}] HTF uyumsuz sinyal: {signal_type} ({signal_direction}) - Skor: {confluence_score}")
        
        # KURAL 1: Trend Önceliği - HTF yönü ile uyumlu sinyalleri her zaman önceliklendir
        if htf_compatible_signals:
            # En yüksek confluence skorlu HTF uyumlu sinyali seç
            best_signal = max(htf_compatible_signals, key=lambda x: x.get('confluence_score', 0))
            best_signal['selection_reason'] = 'HTF_TREND_PRIORITY'
            logger.success(f"[{symbol}] ✅ KURAL 1 - Trend Önceliği: HTF uyumlu sinyal seçildi")
            logger.success(f"[{symbol}] └─ Seçilen: {best_signal.get('type')} ({best_signal.get('direction')}) - Skor: {best_signal.get('confluence_score', 0):.1f}")
            return best_signal
        
        # KURAL 2: Skor Önceliği - HTF uyumsuz sinyaller arasından en yüksek confluence_score'u seç
        if htf_incompatible_signals:
            best_incompatible = max(htf_incompatible_signals, key=lambda x: x.get('confluence_score', 0))
            best_score = best_incompatible.get('confluence_score', 0)
            
            # Reversal sinyaller için minimum kalite eşiği
            if best_score >= 50:  # HTF uyumsuz sinyaller için daha yüksek eşik
                best_incompatible['selection_reason'] = 'HIGHEST_SCORE_REVERSAL'
                logger.success(f"[{symbol}] ✅ KURAL 2 - Skor Önceliği: En yüksek skorlu reversal sinyal seçildi")
                logger.success(f"[{symbol}] └─ Seçilen: {best_incompatible.get('type')} ({best_incompatible.get('direction')}) - Skor: {best_score:.1f}")
                return best_incompatible
            else:
                logger.warning(f"[{symbol}] ⚠️ En yüksek skorlu reversal sinyal kalite eşiğini karşılamıyor: {best_score:.1f}/100 (Min: 50)")
        
        # KURAL 3: Sinyal Yok - Hiçbir sinyal kriterleri karşılamıyor
        logger.info(f"[{symbol}] ❌ KURAL 3 - Hiçbir sinyal seçim kriterlerini karşılamadı")
        return None

    def _validate_input_data(self, symbol: str, all_symbol_data: Dict[str, Any]) -> bool:
        """
        Orkestrasyon için gerekli olan temel verilerin varlığını ve temel yapısını doğrular.
        """
        required_keys = {
            'candles': (pd.DataFrame, False),
            'structure_analysis': (dict, True),
            'liquidity_analysis': (dict, True),
            'fvg_analysis': (list, True),
            'order_block_analysis': (dict, True)
        }

        for key, (expected_type, can_be_empty) in required_keys.items():
            data = all_symbol_data.get(key)
            if data is None:
                logger.error(f"[{symbol}] ❌ Veri Doğrulama Hatası: Gerekli anahtar '{key}' bulunamadı.")
                return False
            
            if not isinstance(data, expected_type):
                logger.error(f"[{symbol}] ❌ Veri Doğrulama Hatası: '{key}' için beklenen tip {expected_type}, alınan {type(data)}.")
                return False
            
            if not can_be_empty and (isinstance(data, (list, dict, pd.DataFrame)) and data.empty):
                 logger.error(f"[{symbol}] ❌ Veri Doğrulama Hatası: '{key}' verisi boş olamaz.")
                 return False
        
        logger.info(f"[{symbol}] ✅ Veri Doğrulama Başarılı: Gerekli tüm veriler mevcut ve doğru formatta.")
        return True
            
    def get_signal_summary(self, signal_data: Dict[str, Any]) -> str:
        """Final sinyal özeti için yardımcı fonksiyon."""
        if not signal_data:
            return "Sinyal bulunamadı"
            
        summary = "🎯 FINAL SİNYAL ÖZET:\n"
        summary += f"├─ 📊 Ana Sinyal: {signal_data.get('type', 'N/A')} ({signal_data.get('direction', 'N/A').upper()})\n"
        
        # Sinyal önceliği ve zamanlama bilgileri
        signal_priority = signal_data.get('signal_priority', 'N/A')
        summary += f"├─ 🔍 Sinyal Önceliği: {signal_priority}\n"
        
        # Confluence skoru
        confluence_score = signal_data.get('confluence_score', 0.0)
        if confluence_score > 0:
            summary += f"├─ 🎯 Confluence Skoru: {confluence_score:.1f}/100\n"
        
        # HTF Yönü ve güven seviyesi
        htf_direction = signal_data.get('htf_direction', 'N/A')
        htf_trend_strength = signal_data.get('htf_trend_strength', 0.0)
        summary += f"├─ 📈 HTF Yönü: {htf_direction} (Güç: {htf_trend_strength:.2f})\n"
        
        # Session context bilgileri
        session_context = signal_data.get('session_context', {})
        if session_context:
            active_killzone = session_context.get('active_killzone')
            if active_killzone:
                summary += f"├─ 🔥 Aktif Killzone: {active_killzone}\n"
            
            active_overlap = session_context.get('active_overlap')
            if active_overlap:
                summary += f"├─ 🌟 Aktif Overlap: {active_overlap}\n"
            
            impact_level = session_context.get('impact_level')
            impact_score = session_context.get('impact_score', 0)
            if impact_level:
                summary += f"├─ ⚡ Impact Seviyesi: {impact_level} ({impact_score}/100)\n"
        
        # Giriş stratejisi ve fiyat seviyeleri
        summary += f"├─ 💼 Giriş Stratejisi: {signal_data.get('strategy_used', 'N/A')}\n"
        summary += f"├─ 💰 Giriş Fiyatı: {signal_data.get('primary_entry')}\n"
        summary += f"├─ 🛡️ Stop Loss: {signal_data.get('stop_loss')}\n"
        
        # Take Profit seviyeleri
        tp1 = signal_data.get('tp1')
        tp1_5 = signal_data.get('tp1_5')
        tp2 = signal_data.get('tp2')
        tp3 = signal_data.get('tp3')
        
        if tp1:
            summary += f"├─ 💵 TP1: {tp1}\n"
        if tp1_5:
            summary += f"├─ 💵 TP1.5: {tp1_5}\n"
        if tp2:
            summary += f"├─ 💵 TP2: {tp2}\n"
        if tp3:
            summary += f"├─ 💵 TP3: {tp3}\n"
        
        # Özel sinyal tipleri için ek bilgiler
        if signal_priority == "KILLZONE_PREMIUM":
            manipulation_analysis = signal_data.get('manipulation_analysis', {})
            if manipulation_analysis:
                manipulation_type = manipulation_analysis.get('manipulation_type', 'N/A')
                manipulation_strength = manipulation_analysis.get('manipulation_strength', 0)
                summary += f"├─ 🎯 Manipülasyon Tipi: {manipulation_type}\n"
                summary += f"├─ 💪 Manipülasyon Gücü: {manipulation_strength}/10\n"
        
        elif signal_priority == "HTF_POI_PREMIUM":
            poi_details = signal_data.get('poi_details', {})
            if poi_details:
                poi_type = poi_details.get('type', 'N/A')
                poi_quality = poi_details.get('quality', 'N/A')
                summary += f"├─ 🎯 POI Tipi: {poi_type}\n"
                summary += f"├─ ⭐ POI Kalitesi: {poi_quality}\n"
        
        elif signal_priority == "LIQUIDITY_HUNT":
            hunt_details = signal_data.get('hunt_details', {})
            if hunt_details:
                hunt_type = hunt_details.get('hunt_type', 'N/A')
                hunt_strength = hunt_details.get('hunt_strength', 0)
                summary += f"├─ 🎯 Likidite Avı Tipi: {hunt_type}\n"
                summary += f"├─ 💪 Likidite Avı Gücü: {hunt_strength}/10\n"
        
        # Confluence kontrolü
        if htf_direction and signal_data.get('direction'):
            htf_dir_normalized = htf_direction.upper()
            signal_dir_normalized = signal_data.get('direction', '').upper()
            
            # Reversal stratejiler için istisna kontrolü
            signal_type = signal_data.get('type', '')
            is_reversal_strategy = any(rev_type in signal_type for rev_type in [
                'LIQSFP_REV', 'REVERSAL', 'HUNT', 'SWEEP', 'MANIPULATION', 
                'WEAK_HIGH_REVERSAL', 'WEAK_LOW_REVERSAL', 'SESSION_MANIPULATION'
            ])
            
            if is_reversal_strategy:
                summary += f"└─ ✅ Reversal Stratejisi: HTF {htf_dir_normalized} ↔ Sinyal {signal_dir_normalized}"
            # Hatalı olan blok burasıydı, BULLISH ile BULL eşleşmiyordu.
            # Kapsamlı bir kontrol ile düzeltelim:
            elif ((htf_dir_normalized == 'BULLISH' and signal_dir_normalized in ['BULL', 'BUY', 'LONG', 'BULLISH']) or 
                  (htf_dir_normalized == 'BEARISH' and signal_dir_normalized in ['BEAR', 'SELL', 'SHORT', 'BEARISH'])):
                summary += f"└─ ✅ Confluence: HTF {htf_dir_normalized} ↔ Sinyal {signal_dir_normalized}"
            else:
                summary += f"└─ ⚠️ Confluence Uyumsuz: HTF {htf_dir_normalized} ↔ Sinyal {signal_dir_normalized}"
        else:
            summary += f"└─ ⚠️ Confluence bilgisi eksik"
        
        return summary
        
    def log_final_signal_summary(self, symbol: str, signal_data: Dict[str, Any]):
        """Final sinyalinin özetini loglar."""
        summary = self.get_signal_summary(signal_data)
        logger.success(f"[{symbol}] 🎯 FİNAL SİNYALİ OLUŞTURULDU\n{summary}")