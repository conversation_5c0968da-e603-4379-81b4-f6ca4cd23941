"""
ICT 2022 Mentorship Model Analyzer
Bu modül, ICT'nin 2022 mentorship modelini uygular ve diğer analizörlerin çıktılarını kullanarak
durum makinesi mantığıyla çalışır.
"""

from loguru import logger
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import pandas as pd


class MentorshipModelAnalyzer:
    """
    ICT 2022 Mentorship Model'ini uygulayan analizör.
    Durum makinesi mantığıyla çalışır ve diğer analizörlerin çıktılarını tüketir.
    """
    
    def __init__(self):
        """Ana<PERSON><PERSON><PERSON>rü başlatır."""
        # Her sembol için modelin hangi aşamada olduğunu takip eder
        # Örn: {'BTCUSDT': {'state': 'WAITING_FOR_LIQUIDITY_GRAB', 'data': {...}}}
        self.state: Dict[str, Dict] = {}
        
        # Model durumları
        self.STATES = {
            'IDLE': 'Trend bekleniyor',
            'WAITING_FOR_LIQUIDITY_GRAB': 'Likidite alımı bekleniyor',
            'WAITING_FOR_MSS': 'Market Structure Shift bekleniyor',
            'SIGNAL_READY': 'Sinyal hazır'
        }
        
        logger.info("ICT 2022 Mentorship Model Analyzer başlatıldı")
    
    def analyze(self, symbol: str, all_symbol_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Ana analiz fonksiyonu. 2022 modelinin adımlarını sırayla takip eder.
        
        Args:
            symbol: Analiz edilecek sembol
            all_symbol_data: Tüm analizörlerin çıktıları
            
        Returns:
            List[Dict]: Oluşturulan sinyaller listesi
        """
        try:
            # 1. Yönelim Belirleme (HTF Bias)
            htf_structure = all_symbol_data.get('htf_12h_structure', {})
            htf_trend = htf_structure.get('current_trend', 'sideways')
            
            if htf_trend == 'sideways':
                self.state[symbol] = {'state': 'IDLE', 'reason': 'HTF trend belirsiz'}
                return []
            
            # 2. Likidite Hedeflerini Belirleme
            liquidity_analysis = all_symbol_data.get('liquidity_analysis', {})
            target_liquidity, grab_liquidity = self._determine_liquidity_targets(
                htf_trend, liquidity_analysis
            )
            
            if not target_liquidity or not grab_liquidity:
                self.state[symbol] = {'state': 'IDLE', 'reason': 'Likidite hedefleri net değil'}
                return []
            
            # 3. Durum Makinesini Çalıştır
            return self._run_state_machine(symbol, all_symbol_data, htf_trend, 
                                         target_liquidity, grab_liquidity)
            
        except Exception as e:
            logger.error(f"[{symbol}] 2022 Model analizi hatası: {e}")
            return []
    
    def _determine_liquidity_targets(self, htf_trend: str, liquidity_analysis: Dict) -> tuple:
        """
        HTF trend'e göre likidite hedeflerini belirler.
        
        Args:
            htf_trend: Yüksek zaman dilimi trend yönü
            liquidity_analysis: Likidite analizi sonuçları
            
        Returns:
            tuple: (target_liquidity, grab_liquidity)
        """
        bsl_zones = liquidity_analysis.get('bsl_zones', [])
        ssl_zones = liquidity_analysis.get('ssl_zones', [])
        
        if not bsl_zones or not ssl_zones:
            return None, None
        
        if htf_trend == 'bullish':
            # Hedef: En yakın BSL (yukarı), Temizlenmesi gereken: En yakın SSL (aşağı)
            target_liquidity = min(bsl_zones, key=lambda z: z['price'])
            grab_liquidity = max(ssl_zones, key=lambda z: z['price'])
        else:  # bearish
            # Hedef: En yakın SSL (aşağı), Temizlenmesi gereken: En yakın BSL (yukarı)
            target_liquidity = max(ssl_zones, key=lambda z: z['price'])
            grab_liquidity = min(bsl_zones, key=lambda z: z['price'])
        
        return target_liquidity, grab_liquidity
    
    def _run_state_machine(self, symbol: str, all_symbol_data: Dict, htf_trend: str,
                          target_liquidity: Dict, grab_liquidity: Dict) -> List[Dict]:
        """
        Durum makinesini çalıştırır ve model adımlarını takip eder.
        
        Args:
            symbol: Sembol adı
            all_symbol_data: Tüm analiz verileri
            htf_trend: HTF trend yönü
            target_liquidity: Hedef likidite seviyesi
            grab_liquidity: Temizlenecek likidite seviyesi
            
        Returns:
            List[Dict]: Oluşturulan sinyaller
        """
        current_state = self.state.get(symbol, {}).get('state', 'IDLE')
        
        # Adım A: Likidite Alımını Bekle
        if current_state == 'IDLE':
            self.state[symbol] = {
                'state': 'WAITING_FOR_LIQUIDITY_GRAB',
                'grab_target': grab_liquidity,
                'final_target': target_liquidity,
                'htf_trend': htf_trend,
                'start_time': datetime.now()
            }
            logger.info(f"[{symbol}] 2022 MODEL ADIM 1: Likidite alımı bekleniyor - {grab_liquidity['price']}")
            return []
        
        # Adım B: Likidite Alındı mı Kontrol Et
        elif current_state == 'WAITING_FOR_LIQUIDITY_GRAB':
            if self._check_liquidity_grab(all_symbol_data, self.state[symbol]['grab_target']):
                logger.success(f"[{symbol}] 2022 MODEL ADIM 2: Likidite alımı gerçekleşti! MSS bekleniyor...")
                self.state[symbol]['state'] = 'WAITING_FOR_MSS'
                self.state[symbol]['grab_time'] = datetime.now()
            return []
        
        # Adım C: MSS Oluşumunu Bekle
        elif current_state == 'WAITING_FOR_MSS':
            mss_result = self._check_for_mss(all_symbol_data, htf_trend)
            if mss_result['mss_found']:
                logger.success(f"[{symbol}] 2022 MODEL ADIM 3: MSS teyit edildi! Sinyal oluşturuluyor...")
                self.state[symbol]['state'] = 'SIGNAL_READY'
                return self._create_2022_model_signal(symbol, mss_result, self.state[symbol])
            return []
        
        return []
    
    def _check_liquidity_grab(self, all_symbol_data: Dict, grab_target: Dict) -> bool:
        """
        Likidite alımının gerçekleşip gerçekleşmediğini kontrol eder.
        
        Args:
            all_symbol_data: Tüm analiz verileri
            grab_target: Temizlenecek likidite hedefi
            
        Returns:
            bool: Likidite alımı gerçekleştiyse True
        """
        try:
            candles = all_symbol_data.get('candles', pd.DataFrame())
            if candles.empty:
                return False
            
            # Son 5 mum içinde hedef fiyata dokunup dokunmadığını kontrol et
            recent_candles = candles.tail(5)
            target_price = grab_target['price']
            
            if grab_target['type'] == 'SSL':
                # SSL için low'ların hedef fiyatın altına inip inmediğini kontrol et
                return any(recent_candles['low'] <= target_price)
            else:  # BSL
                # BSL için high'ların hedef fiyatın üstüne çıkıp çıkmadığını kontrol et
                return any(recent_candles['high'] >= target_price)
                
        except Exception as e:
            logger.error(f"Likidite alımı kontrolü hatası: {e}")
            return False
    
    def _check_for_mss(self, all_symbol_data: Dict, htf_trend: str) -> Dict:
        """
        Market Structure Shift (MSS) oluşumunu kontrol eder.
        
        Args:
            all_symbol_data: Tüm analiz verileri
            htf_trend: HTF trend yönü
            
        Returns:
            Dict: MSS analiz sonuçları
        """
        try:
            # Market structure analizinden MSS sinyallerini al
            market_structure = all_symbol_data.get('market_structure_analysis', {})
            mss_signals = market_structure.get('mss_signals', [])
            
            if not mss_signals:
                return {'mss_found': False}
            
            # Son MSS sinyalini kontrol et
            latest_mss = mss_signals[-1]
            
            # HTF trend ile uyumlu MSS var mı kontrol et
            if htf_trend == 'bullish' and latest_mss.get('direction') == 'bullish':
                return {
                    'mss_found': True,
                    'mss_data': latest_mss,
                    'confirmation_level': latest_mss.get('strength', 0.5)
                }
            elif htf_trend == 'bearish' and latest_mss.get('direction') == 'bearish':
                return {
                    'mss_found': True,
                    'mss_data': latest_mss,
                    'confirmation_level': latest_mss.get('strength', 0.5)
                }
            
            return {'mss_found': False}
            
        except Exception as e:
            logger.error(f"MSS kontrolü hatası: {e}")
            return {'mss_found': False}
    
    def _create_2022_model_signal(self, symbol: str, mss_result: Dict, state_data: Dict) -> List[Dict]:
        """
        2022 model sinyali oluşturur.
        
        Args:
            symbol: Sembol adı
            mss_result: MSS analiz sonuçları
            state_data: Durum verileri
            
        Returns:
            List[Dict]: Oluşturulan sinyal listesi
        """
        try:
            signal = {
                'type': 'ICT_2022_MODEL',
                'symbol': symbol,
                'direction': state_data['htf_trend'],
                'entry_price': mss_result['mss_data'].get('price', 0),
                'target_price': state_data['final_target']['price'],
                'confidence': min(0.95, mss_result.get('confirmation_level', 0.8) + 0.15),
                'confluence_score': min(0.95, mss_result.get('confirmation_level', 0.8) + 0.15) * 100,  # SignalOrchestrator için gerekli
                'priority_level': 0,  # En yüksek öncelik (0 = en yüksek)
                'reasoning': f"ICT 2022 Model: Likidite alımı → MSS teyidi → {state_data['htf_trend'].upper()} sinyal",
                'metadata': {
                    'grab_target': state_data['grab_target'],
                    'final_target': state_data['final_target'],
                    'mss_data': mss_result['mss_data'],
                    'model_completion_time': datetime.now().isoformat(),
                    'total_setup_duration': (datetime.now() - state_data['start_time']).total_seconds()
                }
            }
            
            # Durumu sıfırla
            self.state[symbol] = {'state': 'IDLE', 'reason': 'Sinyal oluşturuldu'}
            
            logger.success(f"[{symbol}] 🏆 ICT 2022 MODEL SİNYALİ OLUŞTURULDU: {signal['direction'].upper()}")
            
            return [signal]
            
        except Exception as e:
            logger.error(f"[{symbol}] 2022 model sinyali oluşturma hatası: {e}")
            return []
    
    def get_state_info(self, symbol: str) -> Dict:
        """
        Belirtilen sembol için mevcut durum bilgisini döndürür.
        
        Args:
            symbol: Sembol adı
            
        Returns:
            Dict: Durum bilgisi
        """
        state_data = self.state.get(symbol, {'state': 'IDLE'})
        return {
            'current_state': state_data.get('state', 'IDLE'),
            'state_description': self.STATES.get(state_data.get('state', 'IDLE'), 'Bilinmeyen durum'),
            'state_data': state_data
        }
    
    def reset_state(self, symbol: str) -> None:
        """
        Belirtilen sembol için durumu sıfırlar.
        
        Args:
            symbol: Sembol adı
        """
        if symbol in self.state:
            del self.state[symbol]
            logger.info(f"[{symbol}] 2022 Model durumu sıfırlandı")