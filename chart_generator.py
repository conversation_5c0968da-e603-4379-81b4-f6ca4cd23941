# chart_generator.py

import os
from typing import Dict, Optional, List, Any, Tuple
import pandas as pd
import mplfinance as mpf
from loguru import logger
from datetime import datetime

class ChartGenerator:
    """
    mplfinance kütü<PERSON><PERSON>ini kullanarak sinyaller ve analizler için
    detaylı mum grafikleri oluşturur.
    """
    def __init__(self, chart_dir: str = "charts"):
        self.chart_dir = chart_dir
        if not os.path.exists(self.chart_dir):
            os.makedirs(self.chart_dir)
        logger.info("ChartGenerator başlatıldı.")

    def _prepare_plot_data(self, df: pd.DataFrame, analysis_data: Dict[str, Any]) -> Tuple[List, List, List]:
        """Grafiğe eklenecek ek çizimleri (addplot) ve bölgeleri (fill_between) hazırlar."""
        addplots = []
        fills = []
        custom_hlines = []

        # FVG ve Order Block Bölgeleri
        for key, color, alpha in [('fvg_analysis', 'purple', 0.15), 
                                  ('order_block_analysis', 'gray', 0.15)]:
            analysis_result = analysis_data.get(key, {})
            items = []
            if isinstance(analysis_result, list):
                items = analysis_result
            elif isinstance(analysis_result, dict):
                items = analysis_result.get('bullish_obs', []) + analysis_result.get('bearish_obs', [])

            for item in items:
                if item.get('mitigated'): continue
                start = item.get('start_time', item.get('timestamp'))
                end = item.get('end_time', start)
                fills.append(dict(
                    y1=item['bottom'], y2=item['top'],
                    where=(df.index >= start) & (df.index <= end),
                    color=color, alpha=alpha
                ))

        # Likidite Seviyeleri
        liquidity_analysis = analysis_data.get('liquidity_analysis', {})
        bsl_zones = liquidity_analysis.get('external_liquidity', {}).get('bsl_zones', [])
        ssl_zones = liquidity_analysis.get('external_liquidity', {}).get('ssl_zones', [])
        
        for bsl in bsl_zones:
            custom_hlines.append(dict(y=bsl['price'], color='mediumseagreen', linestyle='--', linewidths=0.8))
        for ssl in ssl_zones:
            custom_hlines.append(dict(y=ssl['price'], color='lightcoral', linestyle='--', linewidths=0.8))

        # Pivotlar ve Market Structure Kırılımları
        structure = analysis_data.get('structure_analysis', {})
        pivots = structure.get('major_pivots', [])
        breaks = structure.get('breaks', [])

        if pivots:
            pivot_high_series = pd.Series(float('nan'), index=df.index)
            pivot_low_series = pd.Series(float('nan'), index=df.index)
            for p in pivots:
                if p['timestamp'] in df.index:
                    if p['type'] == 'high':
                        pivot_high_series.loc[p['timestamp']] = p['price'] * 1.005 # Mesafeyi artır
                    else:
                        pivot_low_series.loc[p['timestamp']] = p['price'] * 0.995 # Mesafeyi artır
            
            # Boş olmayan serileri kontrol et ve ekle
            if pivot_high_series.notna().any():
                addplots.append(mpf.make_addplot(pivot_high_series, type='scatter', marker='v', color='red', markersize=40))
            if pivot_low_series.notna().any():
                addplots.append(mpf.make_addplot(pivot_low_series, type='scatter', marker='^', color='green', markersize=40))

        if breaks:
            mss_points = pd.Series(float('nan'), index=df.index)
            msb_points = pd.Series(float('nan'), index=df.index)
            for b in breaks:
                if b['timestamp'] in df.index:
                    price = b['price']
                    if b['type'] == 'MSS':
                        mss_points.loc[b['timestamp']] = price
                    elif b['type'] == 'MSB':
                        msb_points.loc[b['timestamp']] = price
            
            # Boş olmayan serileri kontrol et ve ekle
            if mss_points.notna().any():
                addplots.append(mpf.make_addplot(mss_points, type='scatter', marker='x', color='orange', markersize=60))
            if msb_points.notna().any():
                addplots.append(mpf.make_addplot(msb_points, type='scatter', marker='s', color='blue', markersize=50))

        return addplots, fills, custom_hlines

    def create_signal_chart(
        self,
        symbol: str,
        timeframe: str, # Zaman dilimini argüman olarak al
        candles: pd.DataFrame,
        signal_data: Dict[str, Any],
        analysis_data: Dict[str, Any]
    ) -> Optional[str]:
        """
        Verilen sinyal ve analiz verileri için detaylı bir mum grafiği oluşturur ve kaydeder.
        """
        if candles.empty or len(candles) < 20:
            logger.warning(f"[{symbol}] Grafik oluşturmak için yetersiz mum verisi ({len(candles)} mum).")
            return None

        try:
            df = candles.copy().set_index('timestamp')
            df.index.name = 'Date'
            chart_df = df.tail(100)

            addplots, fills, custom_hlines = self._prepare_plot_data(chart_df, analysis_data)

            hlines_list = [p for p in [signal_data.get('primary_entry'), signal_data.get('stop_loss'), signal_data.get('tp1')] if p is not None]
            num_signal_lines = len(hlines_list)
            colors_list = ['blue', 'red', 'green'][:num_signal_lines]
            linewidths_list = [1.5] * num_signal_lines
            linestyle_list = ['-.'] * num_signal_lines

            if custom_hlines:
                hlines_list.extend([h['y'] for h in custom_hlines])
                colors_list.extend([h['color'] for h in custom_hlines])
                linestyle_list.extend([h['linestyle'] for h in custom_hlines])
                linewidths_list.extend([h['linewidths'] for h in custom_hlines])

            final_hlines_config = dict(
                hlines=hlines_list,
                colors=colors_list,
                linestyle=linestyle_list,
                linewidths=linewidths_list
            )

            mc = mpf.make_marketcolors(up='#26a69a', down='#ef5350', inherit=True)
            style = mpf.make_mpf_style(marketcolors=mc, gridstyle=':', y_on_right=False)

            chart_filename = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            output_path = os.path.join(self.chart_dir, chart_filename)
            
            # Sağ tarafa boşluk eklemek için x ekseni limitlerini ayarla
            padding = 5 # 5 mumluk boşluk
            x_lim = (chart_df.index[0], chart_df.index[-1] + pd.Timedelta(hours=4 * padding)) # 4 saatlik mumlar için

            mpf.plot(
                chart_df,
                type='candle',
                style=style,
                title=f"\n{symbol} - {timeframe} Sinyal Analizi", # Başlığa zaman dilimini ekle
                ylabel='Fiyat (USDT)',
                volume=False,
                hlines=final_hlines_config,
                addplot=addplots if addplots else None,
                fill_between=fills if fills else None,
                savefig=dict(fname=output_path, dpi=150, pad_inches=0.25),
                figratio=(16, 9),
                tight_layout=True,
                xlim=x_lim # X ekseni limitlerini ayarla
            )
            logger.success(f"[{symbol}] Detaylı sinyal grafiği başarıyla oluşturuldu: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"[{symbol}] Grafik oluşturulurken hata oluştu: {e}", exc_info=True)
            return None