# fibonacci_analyzer.py

import pandas as pd
from typing import Dict, Any
from loguru import logger

class FibonacciAnalyzer:
    """
    Tüm Fibonacci tabanlı analizleri merkezi olarak yürüten modül.
    - Yüksek zaman dilimine göre Premium & Discount bölgelerini belirler.
    - Belirli bir itki ayağı için Optimal Trade Entry (OTE) seviyelerini hesaplar.
    """

    def analyze_premium_discount(self, daily_candles: pd.DataFrame, current_price: float) -> dict:
        """
        Günlük (1D) zaman dilimini referans alarak mevcut fiyatın Premium,
        Discount veya Equilibrium (Denge) bölgesinde olup olmadığını belirler.

        Kural: Analiz, bir önceki işlem gününün en yüksek ve en düşük
               seviyeleri arasına çekilen Fibonacci ile yapılır.

        Args:
            daily_candles (pd.DataFrame): 'high', 'low' sütunlarını içeren GÜNLÜK mum verileri.
                                          En az son 2 günü içermelidir.
            current_price (float): Mevcut anlık fiyat.

        Returns:
            dict: Fiyatın bölgesi ve ilgili Fibonacci seviyeleri hakkında bilgi.
        """
        try:
            if daily_candles is None or len(daily_candles) < 2:
                logger.warning("Premium/Discount analizi için yetersiz günlük veri (< 2 gün).")
                return {'zone': 'Unknown', 'message': 'Yetersiz günlük veri.', 'error': True}

            # Mevcut günden bir önceki işlem gününü al
            previous_day = daily_candles.iloc[-2]
            prev_high = previous_day['high']
            prev_low = previous_day['low']

            if prev_high <= prev_low:
                logger.error(f"Geçersiz günlük veri: High ({prev_high}) <= Low ({prev_low})")
                return {'zone': 'Unknown', 'message': 'Geçersiz fiyat verileri.', 'error': True}

            equilibrium = (prev_high + prev_low) / 2.0  # Fibonacci 0.5 seviyesi

            zone = "Equilibrium"
            if current_price > equilibrium:
                zone = "Premium"  # Fiyat pahalı, satış (short) işlemleri aranır.
            elif current_price < equilibrium:
                zone = "Discount" # Fiyat ucuz, alış (long) işlemleri aranır.
                
            logger.info(f"Günlük Premium/Discount Analizi: Bölge={zone} (Mevcut Fiyat: {current_price}, Denge: {equilibrium})")

            return {
                'zone': zone,
                'equilibrium': equilibrium,
                'range_high': prev_high,
                'range_low': prev_low,
                'error': False
            }

        except Exception as e:
            logger.error(f"Premium/Discount analizi sırasında hata: {e}", exc_info=True)
            return {'zone': 'Unknown', 'message': f'Analiz hatası: {str(e)}', 'error': True}

    def calculate_ote_levels(self, impulse_leg_start: float, impulse_leg_end: float, direction: str) -> dict:
        """
        Belirli bir itki ayağı (impulse leg) için Optimal Trade Entry (OTE)
        seviyelerini hesaplar.

        Kural: OTE bölgesi, Fibonacci'nin %62, %70.5 ve %79 seviyeleri arasıdır.

        Args:
            impulse_leg_start (float): İtki ayağının başlangıç fiyatı (Swing High/Low).
            impulse_leg_end (float): İtki ayağının bitiş fiyatı (yeni Swing High/Low).
            direction (str): İşlem yönü. 'bullish' veya 'bearish'.

        Returns:
            dict: OTE seviyelerini içeren bir sözlük.
        """
        try:
            if direction not in ['bullish', 'bearish']:
                logger.error(f"Geçersiz yön: {direction}. 'bullish' veya 'bearish' olmalı.")
                return {'error': True, 'message': f'Geçersiz yön: {direction}'}

            if impulse_leg_start <= 0 or impulse_leg_end <= 0:
                logger.error(f"Geçersiz fiyat verileri: start={impulse_leg_start}, end={impulse_leg_end}")
                return {'error': True, 'message': 'Geçersiz fiyat verileri'}

            leg_high = max(impulse_leg_start, impulse_leg_end)
            leg_low = min(impulse_leg_start, impulse_leg_end)
            leg_range = leg_high - leg_low

            if leg_range <= 0:
                logger.warning(f"Sıfır veya negatif leg aralığı: {leg_range}")
                return {'error': True, 'message': 'Geçersiz leg aralığı'}

            ote_levels = {'error': False}
            
            if direction == 'bullish':  # Alış için geri çekilme (discount) beklenir
                ote_62 = leg_high - (leg_range * 0.62)
                ote_70_5 = leg_high - (leg_range * 0.705)  # Sweet Spot
                ote_79 = leg_high - (leg_range * 0.79)
                ote_levels.update({
                    'entry_zone_top': ote_62,
                    'entry_zone_bottom': ote_79,
                    'sweet_spot': ote_70_5,
                    'direction': 'bullish'
                })
            
            elif direction == 'bearish':  # Satış için yukarı tepki (premium) beklenir
                ote_62 = leg_low + (leg_range * 0.62)
                ote_70_5 = leg_low + (leg_range * 0.705)  # Sweet Spot
                ote_79 = leg_low + (leg_range * 0.79)
                ote_levels.update({
                    'entry_zone_top': ote_79,
                    'entry_zone_bottom': ote_62,
                    'sweet_spot': ote_70_5,
                    'direction': 'bearish'
                })
            
            logger.info(f"{direction.capitalize()} OTE seviyeleri hesaplandı: Giriş Aralığı=({ote_levels['entry_zone_bottom']:.4f} - {ote_levels['entry_zone_top']:.4f})")
            return ote_levels

        except Exception as e:
            logger.error(f"OTE seviyeleri hesaplanırken hata: {e}", exc_info=True)
            return {'error': True, 'message': f'OTE hesaplama hatası: {str(e)}'}

    def get_full_retracement_levels(self, leg_start: float, leg_end: float) -> dict:
        """
        Bir itki ayağının tüm standart Fibonacci seviyelerini hesaplar.
        
        Bu metod, BOS sonrası oluşan impulse leg için geniş kapsamlı Fibonacci 
        seviyelerini hesaplayarak, hiyerarşik giriş stratejisine veri sağlar.
        
        Args:
            leg_start (float): İtki ayağının başlangıç fiyatı
            leg_end (float): İtki ayağının bitiş fiyatı
            
        Returns:
            dict: Tüm Fibonacci seviyelerinin fiyat karşılıkları
        """
        try:
            if leg_start <= 0 or leg_end <= 0:
                logger.error(f"Geçersiz fiyat verileri: start={leg_start}, end={leg_end}")
                return {'error': True, 'message': 'Geçersiz fiyat verileri'}
                
            leg_range = abs(leg_end - leg_start)
            if leg_range <= 0:
                logger.warning(f"Sıfır leg aralığı: {leg_range}")
                return {'error': True, 'message': 'Geçersiz leg aralığı'}
            
            is_bullish = leg_end > leg_start
            
            # Genişletilmiş Fibonacci oranları (negatif seviyeleri de dahil)
            fib_ratios = {
                # Negatif genişletme seviyeleri
                '-0.114': -0.114,
                '-0.214': -0.214,
                
                # Standart geri çekilme seviyeleri
                '0.0': 0.0,      # Leg sonu (100% genişletme noktası)
                '0.114': 0.114,  # Zayıf geri çekilme
                '0.214': 0.214,  # Hafif geri çekilme  
                '0.295': 0.295,  # ICT seviyesi
                '0.34': 0.34,    # ICT seviyesi
                '0.382': 0.382,  # Klasik Fibonacci seviyesi
                '0.5': 0.5,      # Equilibrium (Denge)
                '0.618': 0.618,  # Altın Oran (OTE başlangıcı)
                '0.66': 0.66,    # ICT Altın Oran cebi
                '0.705': 0.705,  # Sweet Spot (OTE en iyi noktası)
                '0.786': 0.786,  # Güçlü geri çekilme (OTE sonu)
                '0.886': 0.886,  # Çok güçlü geri çekilme
                '1.0': 1.0,      # Tam geri çekilme (leg başlangıcı)
                
                # Genişletme seviyeleri 
                '1.114': 1.114,  # Hafif genişletme
                '1.214': 1.214,  # Orta genişletme
                '1.272': 1.272,  # Standart hedef
                '1.382': 1.382,  # Güçlü genişletme
                '1.618': 1.618,  # Altın oran genişletme
                '2.0': 2.0,      # %100 projeksiyon
            }
            
            levels = {'error': False, 'is_bullish': is_bullish}
            
            for ratio_name, ratio in fib_ratios.items():
                if is_bullish:
                    # Yükselen trendde, tepe noktasından geri çekilme hesaplanır
                    price = leg_end - (leg_range * ratio)
                else:
                    # Düşen trendde, dip noktasından tepki hesaplanır  
                    price = leg_end + (leg_range * ratio)
                    
                levels[ratio_name] = round(price, 6)
            
            # Özel gruplamalar (kolay erişim için) - scoring_system ile uyumlu hale getirildi
            zones = {
                'golden_spot1': {'start': min(levels['0.34'], levels['0.382']), 'end': max(levels['0.34'], levels['0.382'])},
                'golden_spot2': {'start': min(levels['0.618'], levels['0.66']), 'end': max(levels['0.618'], levels['0.66'])},
                'ote': {'start': min(levels['0.705'], levels['0.786']), 'end': max(levels['0.705'], levels['0.786'])},
                'ext_zone1': {'start': min(levels['0.114'], levels['0.214']), 'end': max(levels['0.114'], levels['0.214'])},
                'ext_zone2': {'start': min(levels['0.786'], levels['0.886']), 'end': max(levels['0.786'], levels['0.886'])},
                'ext_zone3': {'start': min(levels['1.114'], levels['1.214']), 'end': max(levels['1.114'], levels['1.214'])},
                'neg_ext_zone': {'start': min(levels['-0.114'], levels['-0.214']), 'end': max(levels['-0.114'], levels['-0.214'])}
            }

            # Geriye dönük uyumluluk ve diğer potansiyel kullanımlar için eski anahtarları da ekleyelim
            levels['ote_zone'] = zones['ote']
            levels['golden_pocket'] = zones['golden_spot2']
            
            # Ana dönüş değerine 'zones' anahtarını ekle
            levels['zones'] = zones
            
            logger.info(f"Kapsamlı Fibonacci seviyeleri ve bölgeleri hesaplandı: "
                       f"{'Bullish' if is_bullish else 'Bearish'} leg için "
                       f"{len(fib_ratios)} seviye ve {len(zones)} bölge")
            
            return levels
            
        except Exception as e:
            logger.error(f"Kapsamlı Fibonacci seviyeleri hesaplanırken hata: {e}", exc_info=True)
            return {'error': True, 'message': f'Fibonacci hesaplama hatası: {str(e)}'}
    
    def find_price_at_fibonacci_level(self, levels: dict, target_price: float, 
                                     tolerance_pct: float = 0.5) -> dict:
        """
        Verilen fiyatın hangi Fibonacci seviyesine yakın olduğunu bulur.
        
        Args:
            levels (dict): get_full_retracement_levels'dan dönen seviye sözlüğü
            target_price (float): Kontrol edilecek fiyat
            tolerance_pct (float): Tolerans yüzdesi (varsayılan %0.5)
            
        Returns:
            dict: En yakın seviye bilgileri
        """
        try:
            if levels.get('error', True):
                return {'found': False, 'message': 'Geçersiz seviye verisi'}
            
            closest_level = None
            min_distance = float('inf')
            
            # Standart seviyeleri kontrol et
            fib_ratios = ['0.0', '0.114', '0.214', '0.295', '0.34', '0.382', '0.5', 
                         '0.618', '0.66', '0.705', '0.786', '0.886', '1.0']
            
            for ratio in fib_ratios:
                if ratio in levels:
                    level_price = levels[ratio]
                    distance_pct = abs((target_price - level_price) / level_price) * 100
                    
                    if distance_pct <= tolerance_pct and distance_pct < min_distance:
                        min_distance = distance_pct
                        closest_level = {
                            'ratio': ratio,
                            'price': level_price,
                            'distance_pct': distance_pct,
                            'zone_type': self._get_zone_type(ratio)
                        }
            
            if closest_level:
                logger.debug(f"Fiyat {target_price} → Fibonacci %{closest_level['ratio']} seviyesine "
                           f"{closest_level['distance_pct']:.2f}% yakınlık")
                return {'found': True, 'level': closest_level}
            else:
                return {'found': False, 'message': f'%{tolerance_pct} tolerans içinde seviye bulunamadı'}
                
        except Exception as e:
            logger.error(f"Fibonacci seviye bulma hatası: {e}", exc_info=True)
            return {'found': False, 'message': f'Analiz hatası: {str(e)}'}
    
    def calculate_bos_fibonacci_levels(self, break_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        BOS sonrası oluşan impulse leg için Fibonacci seviyelerini hesaplar.
        Bu fonksiyon, smart_entry_strategy'nin "Tek Doğruluk Kaynağı" prensibine uygun olarak
        tüm BOS Fibonacci analizlerini merkezi olarak yapar.
        
        Args:
            break_data: BOS/MSS break verisi (direction, leg_start, leg_end içermeli)
            
        Returns:
            dict: BOS sonrası impulse leg için hesaplanmış Fibonacci seviyeleri
        """
        try:
            if not break_data or break_data.get('error'):
                logger.error("BOS Fibonacci analizi için geçersiz break_data")
                return {'error': True, 'message': 'Geçersiz break verisi'}
            
            # BOS sonrası impulse leg bilgilerini çıkar
            leg_start = break_data.get('leg_start')
            leg_end = break_data.get('leg_end')
            direction = break_data.get('direction', '').lower()
            
            if not leg_start or not leg_end or not direction:
                logger.error(f"BOS Fibonacci için eksik veri: leg_start={leg_start}, leg_end={leg_end}, direction={direction}")
                return {'error': True, 'message': 'Eksik leg verileri'}
            
            # Impulse leg için kapsamlı Fibonacci seviyelerini hesapla
            fib_levels = self.get_full_retracement_levels(leg_start, leg_end)
            
            if fib_levels.get('error'):
                logger.error(f"BOS Fibonacci seviyeleri hesaplanamadı: {fib_levels.get('message')}")
                return fib_levels
            
            # BOS spesifik bilgileri ekle
            fib_levels['bos_direction'] = direction
            fib_levels['impulse_leg'] = {
                'start': leg_start,
                'end': leg_end,
                'range': abs(leg_end - leg_start)
            }
            
            # OTE seviyelerini de hesapla (geriye dönük uyumluluk için)
            ote_levels = self.calculate_ote_levels(leg_start, leg_end, direction)
            if not ote_levels.get('error'):
                fib_levels['ote_zone'] = ote_levels
            
            logger.info(f"BOS sonrası {direction.upper()} impulse leg için Fibonacci seviyeleri hesaplandı")
            return fib_levels
            
        except Exception as e:
            logger.error(f"BOS Fibonacci seviyeleri hesaplama hatası: {e}", exc_info=True)
            return {'error': True, 'message': f'BOS Fibonacci hesaplama hatası: {str(e)}'}

    def _get_zone_type(self, ratio: str) -> str:
        """Fibonacci oranına göre bölge tipini belirler."""
        ratio_float = float(ratio)
        
        if 0.618 <= ratio_float <= 0.786:
            return 'OTE_ZONE'
        elif 0.618 <= ratio_float <= 0.66:
            return 'GOLDEN_POCKET'
        elif ratio_float == 0.5:
            return 'EQUILIBRIUM'
        elif ratio_float < 0.382:
            return 'WEAK_RETRACEMENT'
        elif ratio_float > 0.886:
            return 'DEEP_RETRACEMENT'
        else:
            return 'STANDARD_LEVEL'
